#!/bin/bash

# 修复枚举类的脚本
# 移除 @AllArgsConstructor 注解并添加构造函数

ENUM_FILES=(
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/EvaluationTypeEnum.java"
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/DataTypeEnum.java"
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/TaskStatusEnum.java"
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/DatasetTypeEnum.java"
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationTaskTypeEnum.java"
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationLabelTypeEnum.java"
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/ColumnTypeEnum.java"
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/GenerateTypeEnum.java"
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationDataItemStatusEnum.java"
)

for file in "${ENUM_FILES[@]}"; do
    echo "Processing $file..."
    
    # 移除 @AllArgsConstructor 导入
    sed -i '' '/import lombok.AllArgsConstructor;/d' "$file"
    
    # 移除 @AllArgsConstructor 注解
    sed -i '' '/@AllArgsConstructor/d' "$file"
    
    echo "Fixed $file"
done

echo "All enum files processed. You need to manually add constructors."
