#!/usr/bin/env python3

import re
import os
from typing import List, Tuple, Dict

# 需要修复的domain模块文件
domain_files = [
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/dataset/DatasetWDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/dataset/DatasetColumnConfig.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/dataset/DatasetDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/dataset/DataItemDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/dataset/DatasetColumnInfoDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/dataset/DatasetGenerateConfig.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/annotation/AnnotationTaskDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/annotation/AnnotationDataItemDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/annotation/AnnotationLabelDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/annotation/AnnotationConfigDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/evaluation/EvaluationIndicatorConfig.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/evaluation/EvaluationTaskDO.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/request/AnnotationTaskUpdateRequest.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/request/DatasetUpdateRequest.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/request/AnnotationTaskCreateRequest.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/request/DatasetCreateRequest.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/request/AnnotationTaskDeleteRequest.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/request/DatasetColumnCreateRequest.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/request/DatasetColumnsBatchOperationRequest.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/query/DatasetColumnInfoQuery.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/query/EvaluationTaskQuery.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/query/AnnotationLabelQuery.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/query/DatasetQuery.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/query/AnnotationTaskQuery.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/query/DataItemQuery.java",
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/model/query/AnnotationDataItemQuery.java"
]

# 需要添加@Slf4j的服务类
service_files = [
    "dzusergrowth-operation-domain/src/main/java/com/sankuai/dzusergrowth/operation/domain/service/impl/DatasetDomainServiceImpl.java",
    "dzusergrowth-operation-application/src/main/java/com/sankuai/dzusergrowth/operation/application/service/impl/DatasetAppServiceImpl.java"
]

# API层需要修复的类
api_files = [
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/request/DatasetCreateRequest.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/request/DatasetUpdateRequest.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/request/DatasetDetailRequest.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/request/DatasetListRequest.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/response/DatasetDTO.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/response/DatasetDetailResponse.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/common/PageResult.java"
]

def extract_fields(content: str) -> List[Tuple[str, str]]:
    """提取类中的私有字段"""
    field_pattern = r'private\s+(\w+(?:<[^>]+>)?)\s+(\w+);'
    return re.findall(field_pattern, content)

def generate_getter(field_type: str, field_name: str) -> str:
    """生成getter方法"""
    method_name = f"get{field_name[0].upper()}{field_name[1:]}"
    
    # 特殊处理boolean类型
    if field_type.lower() == 'boolean':
        method_name = f"is{field_name[0].upper()}{field_name[1:]}"
        if field_name.startswith('is') and len(field_name) > 2:
            method_name = field_name
    
    return f"""    /**
     * 获取{field_name}
     *
     * @return {field_name}
     */
    public {field_type} {method_name}() {{
        return {field_name};
    }}"""

def generate_setter(field_type: str, field_name: str) -> str:
    """生成setter方法"""
    method_name = f"set{field_name[0].upper()}{field_name[1:]}"
    
    return f"""    /**
     * 设置{field_name}
     *
     * @param {field_name} {field_name}
     */
    public void {method_name}({field_type} {field_name}) {{
        this.{field_name} = {field_name};
    }}"""

def generate_builder_method(class_name: str, fields: List[Tuple[str, str]]) -> str:
    """生成builder方法"""
    builder_class = f"{class_name}Builder"
    
    # Builder类定义
    builder_methods = []
    for field_type, field_name in fields:
        builder_methods.append(f"""        public {builder_class} {field_name}({field_type} {field_name}) {{
            this.{field_name} = {field_name};
            return this;
        }}""")
    
    # 字段声明
    field_declarations = [f"        private {field_type} {field_name};" for field_type, field_name in fields]
    
    # build方法
    constructor_params = [field_name for _, field_name in fields]
    build_method = f"""        public {class_name} build() {{
            return new {class_name}({', '.join(constructor_params)});
        }}"""
    
    return f"""    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static {builder_class} builder() {{
        return new {builder_class}();
    }}
    
    /**
     * Builder类
     */
    public static class {builder_class} {{
{chr(10).join(field_declarations)}
        
{chr(10).join(builder_methods)}
        
{build_method}
    }}"""

def fix_lombok_class(file_path: str) -> bool:
    """修复单个类的Lombok问题"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取类名
        class_match = re.search(r'public class (\w+)', content)
        if not class_match:
            print(f"Could not find class name in {file_path}")
            return False
        
        class_name = class_match.group(1)
        
        # 提取字段
        fields = extract_fields(content)
        if not fields:
            print(f"No fields found in {file_path}")
            return True
        
        # 检查是否已经有getter/setter方法
        has_getters = bool(re.search(r'public \w+ get\w+\(\)', content))
        has_setters = bool(re.search(r'public void set\w+\(', content))
        has_builder = bool(re.search(r'public static \w+Builder builder\(\)', content))
        
        if has_getters and has_setters and has_builder:
            print(f"Class {class_name} already has getter/setter/builder methods")
            return True
        
        # 生成getter和setter方法
        methods = []
        
        if not has_getters:
            for field_type, field_name in fields:
                methods.append(generate_getter(field_type, field_name))
        
        if not has_setters:
            for field_type, field_name in fields:
                methods.append(generate_setter(field_type, field_name))
        
        if not has_builder:
            methods.append(generate_builder_method(class_name, fields))
        
        if not methods:
            return True
        
        # 找到类的结束位置，在最后一个}之前插入方法
        last_brace_pos = content.rfind('}')
        if last_brace_pos == -1:
            print(f"Could not find class end in {file_path}")
            return False
        
        # 插入方法
        methods_str = '\n\n' + '\n\n'.join(methods) + '\n'
        new_content = content[:last_brace_pos] + methods_str + content[last_brace_pos:]
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Fixed Lombok issues in {class_name}")
        return True
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def add_slf4j_annotation(file_path: str) -> bool:
    """为服务类添加@Slf4j注解"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有@Slf4j注解
        if '@Slf4j' in content:
            print(f"File {file_path} already has @Slf4j annotation")
            return True
        
        # 检查是否有lombok.extern.slf4j.Slf4j导入
        if 'import lombok.extern.slf4j.Slf4j;' not in content:
            # 找到import语句的位置
            import_match = re.search(r'(import [^;]+;)', content)
            if import_match:
                import_pos = import_match.end()
                content = content[:import_pos] + '\nimport lombok.extern.slf4j.Slf4j;' + content[import_pos:]
        
        # 在类声明前添加@Slf4j注解
        class_pattern = r'(public class \w+)'
        class_match = re.search(class_pattern, content)
        if class_match:
            class_pos = class_match.start()
            content = content[:class_pos] + '@Slf4j\n' + content[class_pos:]
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Added @Slf4j annotation to {file_path}")
        return True
        
    except Exception as e:
        print(f"Error adding @Slf4j to {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("Starting Lombok issues fix...")

    # 修复domain类的Lombok问题
    success_count = 0
    for file_path in domain_files:
        if os.path.exists(file_path):
            if fix_lombok_class(file_path):
                success_count += 1
        else:
            print(f"File not found: {file_path}")

    print(f"Fixed {success_count}/{len(domain_files)} domain classes")

    # 修复API类的Lombok问题
    api_success_count = 0
    for file_path in api_files:
        if os.path.exists(file_path):
            if fix_lombok_class(file_path):
                api_success_count += 1
        else:
            print(f"API file not found: {file_path}")

    print(f"Fixed {api_success_count}/{len(api_files)} API classes")

    # 为服务类添加@Slf4j注解
    for file_path in service_files:
        if os.path.exists(file_path):
            add_slf4j_annotation(file_path)
        else:
            print(f"Service file not found: {file_path}")

    print("Lombok issues fix completed!")

if __name__ == "__main__":
    main()
