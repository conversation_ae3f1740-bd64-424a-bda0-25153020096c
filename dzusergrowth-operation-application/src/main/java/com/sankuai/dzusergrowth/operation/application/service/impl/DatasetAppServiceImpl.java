package com.sankuai.dzusergrowth.operation.application.service.impl;

import com.sankuai.dzusergrowth.operation.api.common.PageResult;
import com.sankuai.dzusergrowth.operation.api.request.DatasetCreateRequest;
import com.sankuai.dzusergrowth.operation.api.request.DatasetDetailRequest;
import com.sankuai.dzusergrowth.operation.api.request.DatasetListRequest;
import com.sankuai.dzusergrowth.operation.api.request.DatasetUpdateRequest;
import com.sankuai.dzusergrowth.operation.api.response.DatasetDTO;
import com.sankuai.dzusergrowth.operation.api.response.DatasetDetailResponse;
import com.sankuai.dzusergrowth.operation.application.service.DatasetAppService;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetWDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetGenerateConfig;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetQuery;
import com.sankuai.dzusergrowth.operation.domain.service.DatasetDomainService;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.api.util.DatasetGranularityMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据集应用服务实现类
 * 实现数据集管理的业务逻辑
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class DatasetAppServiceImpl implements DatasetAppService {

    /**
     * 日期时间格式常量
     */
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private DatasetDomainService datasetDomainService;
    
    /**
     * 获取数据集列表
     * 支持分页和多维度筛选
     *
     * @param request 查询请求
     * @return 分页数据集列表
     */
    @Override
    public PageResult<DatasetDTO> list(DatasetListRequest request) {
        log.info("开始查询数据集列表，请求参数: {}", request);
        
        try {
            // 1. 参数校验
            validateListRequest(request);
            
            // 2. 构建查询条件
            DatasetQuery query = buildDatasetQuery(request);
            
            // 3. 调用领域服务查询数据集列表
            PageResult<DatasetDO> domainResult = datasetDomainService.queryDatasetsWithPagination(query);
            
            // 4. 转换为DTO并返回
            return convertToDatasetDTOPageResult(domainResult);
                    
        } catch (Exception e) {
            log.error("查询数据集列表失败", e);
            throw new RuntimeException("查询数据集列表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 新增数据集
     * 支持活动任务和资产两种粒度类型
     *
     * @param request 创建请求
     * @return 数据集ID
     */
    @Override
    public String create(DatasetCreateRequest request) {
        log.info("开始创建数据集，请求参数: {}", request);
        
        try {
            // 1. 参数校验
            validateCreateRequest(request);
            
            // 2. 转换为领域对象
            com.sankuai.dzusergrowth.operation.domain.model.request.DatasetCreateRequest domainRequest = 
                convertToCreateDomainRequest(request);
            
            // 3. 调用领域服务创建数据集
            DatasetDO datasetDO = datasetDomainService.createDataset(domainRequest);
            
            // 4. 返回数据集ID
            String datasetId = String.valueOf(datasetDO.getId());
            
            log.info("数据集创建成功，数据集ID: {}", datasetId);
            return datasetId;
            
        } catch (Exception e) {
            log.error("创建数据集失败", e);
            throw new RuntimeException("创建数据集失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 编辑数据集
     * 仅允许修改数据集的名称和描述信息
     *
     * @param request 更新请求
     * @return 是否成功
     */
    @Override
    public Boolean update(DatasetUpdateRequest request) {
        log.info("开始更新数据集，请求参数: {}", request);
        
        try {
            // 1. 参数校验
            validateUpdateRequest(request);
            
            // 2. 转换为领域对象
            com.sankuai.dzusergrowth.operation.domain.model.request.DatasetUpdateRequest domainRequest = 
                convertToUpdateDomainRequest(request);
            
            // 3. 调用领域服务更新数据集
            DatasetDO updatedDataset = datasetDomainService.updateDataset(domainRequest);
            
            // 4. 返回操作结果
            boolean success = (updatedDataset != null);
            
            log.info("数据集更新成功，数据集ID: {}", request.getDatasetId());
            return success;
            
        } catch (Exception e) {
            log.error("更新数据集失败，数据集ID: {}", request.getDatasetId(), e);
            throw new RuntimeException("更新数据集失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取数据集详情
     * 支持新字段与结构动态扩展
     *
     * @param request 详情查询请求
     * @return 数据集详情
     */
    @Override
    public DatasetDetailResponse detail(DatasetDetailRequest request) {
        log.info("开始查询数据集详情，数据集ID: {}", request.getDatasetId());
        
        try {
            // 1. 参数校验
            validateDetailRequest(request);
            
            // 2. 调用领域服务查询数据集聚合根
            Long datasetId = parseDatasetId(request.getDatasetId());
            DatasetWDO datasetWDO = datasetDomainService.getDatasetAggregateById(datasetId);
            
            // 3. 转换为响应对象并返回
            DatasetDetailResponse response = convertToDatasetDetailResponse(datasetWDO);
                    
            log.info("数据集详情查询成功，数据集ID: {}", request.getDatasetId());
            return response;
            
        } catch (Exception e) {
            log.error("查询数据集详情失败，数据集ID: {}", request.getDatasetId(), e);
            throw new RuntimeException("查询数据集详情失败: " + e.getMessage(), e);
        }
    }
    
    // =============== 私有辅助方法 ===============
    
    /**
     * 校验列表查询请求参数
     */
    private void validateListRequest(DatasetListRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("查询请求不能为空");
        }
        
        // 分页参数校验
        if (request.getPageNo() != null && request.getPageNo() < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        
        if (request.getPageSize() != null && (request.getPageSize() < 1 || request.getPageSize() > 100)) {
            throw new IllegalArgumentException("每页条数必须在1-100之间");
        }
        
        log.debug("列表查询请求参数校验通过");
    }
    
    /**
     * 校验创建请求参数
     */
    private void validateCreateRequest(DatasetCreateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("创建请求不能为空");
        }
        
        if (!StringUtils.hasText(request.getName())) {
            throw new IllegalArgumentException("数据集名称不能为空");
        }
        
        if (request.getName().length() > 50) {
            throw new IllegalArgumentException("数据集名称不能超过50个字符");
        }
        
        if (request.getDescription() != null && request.getDescription().length() > 200) {
            throw new IllegalArgumentException("数据集描述不能超过200个字符");
        }
        
        log.debug("创建请求参数校验通过");
    }
    
    /**
     * 校验更新请求参数
     */
    private void validateUpdateRequest(DatasetUpdateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("更新请求不能为空");
        }
        
        if (!StringUtils.hasText(request.getDatasetId())) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }
        
        log.debug("更新请求参数校验通过");
    }
    
    /**
     * 校验详情查询请求参数
     */
    private void validateDetailRequest(DatasetDetailRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("详情查询请求不能为空");
        }
        
        if (!StringUtils.hasText(request.getDatasetId())) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }
        
        log.debug("详情查询请求参数校验通过");
    }
    
    /**
     * 构建数据集查询条件
     */
    private DatasetQuery buildDatasetQuery(DatasetListRequest request) {
        DatasetQuery.DatasetQueryBuilder builder = DatasetQuery.builder();
        
        // 设置分页参数
        builder.pageNum(request.getPageNo() != null ? request.getPageNo() : 1);
        builder.pageSize(request.getPageSize() != null ? request.getPageSize() : 10);
        
        // 设置查询条件
        if (StringUtils.hasText(request.getNameFilter())) {
            builder.name(request.getNameFilter());
        }
        
        if (StringUtils.hasText(request.getGranularityFilter())) {
            DataGranularityEnum granularityEnum = DatasetGranularityMapper.fromApiGranularity(request.getGranularityFilter());
            if (granularityEnum != null) {
                builder.setDataGranularity(granularityEnum);
            }
        }
        
        // 设置排序
        builder.orderBy("add_time");
        builder.orderDirection("DESC");
        
        return builder.build();
    }
    
    /**
     * 转换domain分页结果为DTO分页结果
     */
    private PageResult<DatasetDTO> convertToDatasetDTOPageResult(PageResult<DatasetDO> domainResult) {
        if (domainResult == null || domainResult.getData() == null) {
            return new PageResult<>(Collections.emptyList(), 0L, 1, 10);
        }
        
        List<DatasetDTO> datasetDTOs = domainResult.getData().stream()
                .map(this::convertToDatasetDTO)
                .collect(Collectors.toList());
        
        return new PageResult<>(
                datasetDTOs, 
                domainResult.getTotal(),
                domainResult.getPageNum(), 
                domainResult.getPageSize()
        );
    }
    
    /**
     * 转换DatasetDO为DatasetDTO
     */
    private DatasetDTO convertToDatasetDTO(DatasetDO datasetDO) {
        if (datasetDO == null) {
            return null;
        }
        
        return DatasetDTO.builder()
                .datasetId(String.valueOf(datasetDO.getId()))
                .name(datasetDO.getName())
                .granularity(datasetDO.getDataGranularity() != null ? datasetDO.getDataGranularity().name() : null)
                .businessScene(datasetDO.getGenerateConfig() != null ? datasetDO.getGenerateConfig().getBusinessScene() : null)
                .creatorMisId(datasetDO.getCreatorId() != null ? String.valueOf(datasetDO.getCreatorId()) : null)
                .description(datasetDO.getDescription())
                .build();
    }
    
    /**
     * 转换API创建请求为domain创建请求
     */
    private com.sankuai.dzusergrowth.operation.domain.model.request.DatasetCreateRequest convertToCreateDomainRequest(
            DatasetCreateRequest request) {
        
        DatasetGenerateConfig generateConfig = DatasetGenerateConfig.builder()
                .businessScene(request.getBusinessScene())
                .collectedMisIds(request.getCollectedMisId())
                .sampleFixedMetrics(request.getSampleFixedMetrics())
                .build();
        
        // 设置时间范围
        setTimeRange(generateConfig, request.getTimeRangeBegin(), request.getTimeRangeEnd());
        
        DataGranularityEnum granularityEnum = DatasetGranularityMapper.fromApiGranularity(request.getGranularity());
        
        return com.sankuai.dzusergrowth.operation.domain.model.request.DatasetCreateRequest.builder()
                .datasetType(DatasetTypeEnum.ORIGINAL) // 默认原始数据集
                .dataGranularity(granularityEnum)
                .name(request.getName())
                .generateType(GenerateTypeEnum.CONDITION_SOURCE) // 默认条件来源
                .generateConfig(generateConfig)
                .description(request.getDescription())
                .creatorId(parseCreatorId(request.getCreatorMisId()))
                .build();
    }
    
    /**
     * 转换API更新请求为domain更新请求
     */
    private com.sankuai.dzusergrowth.operation.domain.model.request.DatasetUpdateRequest convertToUpdateDomainRequest(
            DatasetUpdateRequest request) {

        Long datasetId = parseDatasetId(request.getDatasetId());

        return com.sankuai.dzusergrowth.operation.domain.model.request.DatasetUpdateRequest.builder()
                .datasetId(datasetId)
                .name(request.getName())
                .description(request.getDescription())
                .build();
    }
    
    /**
     * 转换DatasetWDO为DatasetDetailResponse
     */
    private DatasetDetailResponse convertToDatasetDetailResponse(DatasetWDO datasetWDO) {
        if (datasetWDO == null) {
            return null;
        }
        
        DatasetDetailResponse.DatasetDetailResponseBuilder builder = DatasetDetailResponse.builder()
                .datasetId(String.valueOf(datasetWDO.getDatasetId()))
                .name(datasetWDO.getName())
                .description(datasetWDO.getDescription())
                .granularity(datasetWDO.getGranularity() != null ? datasetWDO.getGranularity().name() : null)
                .businessScene(datasetWDO.getBusinessScene())
                .creatorMisId(datasetWDO.getCreatorId() != null ? String.valueOf(datasetWDO.getCreatorId()) : null);
        
        // 转换数据集内容（列信息）
        List<java.util.Map<String, Object>> datasetContents = convertColumnInfoToContents(datasetWDO.getColumnInfoList());
        builder.datasetContents(datasetContents);
        
        return builder.build();
    }

    /**
     * 设置时间范围
     *
     * @param generateConfig 生成配置
     * @param timeRangeBegin 开始时间字符串
     * @param timeRangeEnd 结束时间字符串
     */
    private void setTimeRange(DatasetGenerateConfig generateConfig, String timeRangeBegin, String timeRangeEnd) {
        if (StringUtils.hasText(timeRangeBegin)) {
            Date startTime = parseDateTime(timeRangeBegin);
            if (startTime != null) {
                generateConfig.setStartTime(startTime);
            }
        }

        if (StringUtils.hasText(timeRangeEnd)) {
            Date endTime = parseDateTime(timeRangeEnd);
            if (endTime != null) {
                generateConfig.setEndTime(endTime);
            }
        }
    }

    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @return 解析后的Date对象，解析失败返回null
     */
    private Date parseDateTime(String dateTimeStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_TIME_PATTERN);
            return sdf.parse(dateTimeStr);
        } catch (ParseException e) {
            log.warn("时间格式解析失败: {}", dateTimeStr, e);
            return null;
        }
    }

    /**
     * 转换列信息为数据集内容
     *
     * @param columnInfoList 列信息列表
     * @return 数据集内容列表
     */
    private List<java.util.Map<String, Object>> convertColumnInfoToContents(List<DatasetColumnInfoDO> columnInfoList) {
        if (columnInfoList == null || columnInfoList.isEmpty()) {
            return Collections.emptyList();
        }

        return columnInfoList.stream()
                .map(column -> {
                    java.util.Map<String, Object> map = new java.util.HashMap<>();
                    map.put("name", column.getName());
                    map.put("dataType", column.getDataType());
                    return map;
                })
                .collect(Collectors.toList());
    }

    /**
     * 解析数据集ID
     *
     * @param datasetIdStr 数据集ID字符串
     * @return 解析后的Long对象
     * @throws IllegalArgumentException 如果ID格式无效
     */
    private Long parseDatasetId(String datasetIdStr) {
        try {
            return Long.valueOf(datasetIdStr);
        } catch (NumberFormatException e) {
            log.error("数据集ID格式无效: {}", datasetIdStr, e);
            throw new IllegalArgumentException("数据集ID格式无效: " + datasetIdStr, e);
        }
    }

    /**
     * 解析创建者ID
     *
     * @param creatorMisId 创建者MIS ID字符串
     * @return 解析后的Long对象，如果为空或无效则返回null
     */
    private Long parseCreatorId(String creatorMisId) {
        if (!StringUtils.hasText(creatorMisId)) {
            return null;
        }

        try {
            // 尝试直接解析为Long
            return Long.valueOf(creatorMisId);
        } catch (NumberFormatException e) {
            // 如果不是数字，使用hashCode作为fallback（但记录警告）
            log.warn("创建者ID不是有效数字，使用hashCode作为fallback: {}", creatorMisId);
            return (long) creatorMisId.hashCode();
        }
    }
}