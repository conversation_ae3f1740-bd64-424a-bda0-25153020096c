#!/usr/bin/env python3

import re
import os

# 需要修复的枚举类文件
enum_files = [
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/EvaluationTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/DataTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/TaskStatusEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/DatasetTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationTaskTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationLabelTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/ColumnTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/GenerateTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationDataItemStatusEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AssetTypeEnum.java"
]

def add_getters_to_enum(file_path):
    """为枚举类添加getter方法"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找私有字段定义
    field_pattern = r'private final (\w+) (\w+);'
    fields = re.findall(field_pattern, content)
    
    if not fields:
        print(f"No fields found in {file_path}")
        return
    
    # 构建getter方法
    getters = []
    
    for field_type, field_name in fields:
        # 生成getter方法名
        getter_name = f"get{field_name[0].upper()}{field_name[1:]}"
        
        # 特殊处理一些字段名
        if field_name == "desc":
            getter_name = "getDesc"
        elif field_name == "key":
            getter_name = "getKey"
        
        getter = f"""    /**
     * 获取{field_name}
     *
     * @return {field_name}
     */
    public {field_type} {getter_name}() {{
        return {field_name};
    }}"""
        getters.append(getter)
    
    # 找到构造函数的结束位置，在其后插入getter方法
    constructor_pattern = r'(\w+Enum\([^}]+\}\s*)\n'
    match = re.search(constructor_pattern, content)
    
    if match:
        insert_pos = match.end()
        getters_str = "\n" + "\n\n".join(getters) + "\n"
        new_content = content[:insert_pos] + getters_str + content[insert_pos:]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Added getters to {file_path}")
    else:
        print(f"Could not find constructor in {file_path}")

# 处理所有枚举文件
for file_path in enum_files:
    if os.path.exists(file_path):
        add_getters_to_enum(file_path)
    else:
        print(f"File not found: {file_path}")

print("All enum files processed.")
