#!/usr/bin/env python3

import re
import os

# 需要修复的枚举类文件
enum_files = [
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/EvaluationTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/DatasetTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationTaskTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationLabelTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/ColumnTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/GenerateTypeEnum.java",
    "dzusergrowth-operation-api/src/main/java/com/sankuai/dzusergrowth/operation/api/enums/AnnotationDataItemStatusEnum.java"
]

def add_constructor_to_enum(file_path):
    """为枚举类添加构造函数"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找私有字段定义
    field_pattern = r'private final (\w+) (\w+);'
    fields = re.findall(field_pattern, content)
    
    if not fields:
        print(f"No fields found in {file_path}")
        return
    
    # 获取枚举类名
    class_name_match = re.search(r'public enum (\w+)', content)
    if not class_name_match:
        print(f"Could not find class name in {file_path}")
        return
    
    class_name = class_name_match.group(1)
    
    # 构建构造函数
    constructor_params = []
    constructor_assignments = []
    
    for field_type, field_name in fields:
        constructor_params.append(f"{field_type} {field_name}")
        constructor_assignments.append(f"        this.{field_name} = {field_name};")
    
    constructor = f"""    
    /**
     * 构造函数
     *
{chr(10).join([f"     * @param {field_name} {field_name}" for _, field_name in fields])}
     */
    {class_name}({', '.join(constructor_params)}) {{
{chr(10).join(constructor_assignments)}
    }}"""
    
    # 找到最后一个字段定义的位置，在其后插入构造函数
    last_field_pattern = r'(private final \w+ \w+;)\s*\n'
    matches = list(re.finditer(last_field_pattern, content))
    
    if matches:
        last_match = matches[-1]
        insert_pos = last_match.end()
        new_content = content[:insert_pos] + constructor + "\n" + content[insert_pos:]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Added constructor to {file_path}")
    else:
        print(f"Could not find insertion point in {file_path}")

# 处理所有枚举文件
for file_path in enum_files:
    if os.path.exists(file_path):
        add_constructor_to_enum(file_path)
    else:
        print(f"File not found: {file_path}")

print("All enum files processed.")
