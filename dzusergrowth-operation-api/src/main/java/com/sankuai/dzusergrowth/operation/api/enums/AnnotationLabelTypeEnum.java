package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标签类型枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum AnnotationLabelTypeEnum {
    
    /**
     * 枚举型
     */
    ENUM(1, "枚举型"),

    /**
     * 文本型
     */
    TEXT(2, "文本型");
    
    /**
     * 类型码
     */
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String description;
    
    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static AnnotationLabelTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (AnnotationLabelTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 检查类型码是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 