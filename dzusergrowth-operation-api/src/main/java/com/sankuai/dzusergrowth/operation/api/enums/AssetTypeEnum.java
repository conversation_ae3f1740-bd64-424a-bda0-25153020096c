package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资产类型枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum AssetTypeEnum {
    
    /**
     * 笔记
     */
    NOTE(1, "笔记", "note"),
    
    /**
     * 图片
     */
    IMAGE(2, "图片", "image"),
    
    /**
     * 会场
     */
    VENUE(3, "会场", "venue");
    
    /**
     * 类型码
     */
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String desc;
    
    /**
     * 类型标识
     */
    private final String key;
    
    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static AssetTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (AssetTypeEnum assetType : values()) {
            if (assetType.getCode().equals(code)) {
                return assetType;
            }
        }
        return null;
    }
    
    /**
     * 根据类型码获取枚举（兼容原有方法名）
     *
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static AssetTypeEnum getByCode(Integer code) {
        return fromCode(code);
    }
    
    /**
     * 根据类型标识获取枚举
     *
     * @param key 类型标识
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static AssetTypeEnum fromKey(String key) {
        if (key == null) {
            return null;
        }
        
        for (AssetTypeEnum assetType : values()) {
            if (assetType.getKey().equals(key)) {
                return assetType;
            }
        }
        return null;
    }
    
    /**
     * 检查类型码是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
    
    /**
     * 检查类型标识是否有效
     *
     * @param key 类型标识
     * @return 是否有效
     */
    public static boolean isValidKey(String key) {
        return fromKey(key) != null;
    }
    
    /**
     * 是否为笔记类型
     *
     * @return 是否为笔记类型
     */
    public boolean isNote() {
        return this == NOTE;
    }
    
    /**
     * 是否为图片类型
     *
     * @return 是否为图片类型
     */
    public boolean isImage() {
        return this == IMAGE;
    }
    
    /**
     * 是否为会场类型
     *
     * @return 是否为会场类型
     */
    public boolean isVenue() {
        return this == VENUE;
    }
    
    /**
     * 是否为内容类型（笔记或图片）
     *
     * @return 是否为内容类型
     */
    public boolean isContentType() {
        return this == NOTE || this == IMAGE;
    }
    
    /**
     * 是否为场景类型（会场）
     *
     * @return 是否为场景类型
     */
    public boolean isScenarioType() {
        return this == VENUE;
    }
} 