package com.sankuai.dzusergrowth.operation.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据集创建请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetCreateRequest {
    
    /**
     * 数据集名称（1-50字符）
     */
    private String name;
    
    /**
     * 数据集描述（0-200字符）
     */
    private String description;
    
    /**
     * 数据粒度
     */
    private String granularity; // ACTIVITY_AND_TASK, ASSET
    
    /**
     * 业务场景标识
     */
    private String businessScene;
    
    /**
     * 采集时间开始
     */
    private String timeRangeBegin;
    
    /**
     * 采集时间结束
     */
    private String timeRangeEnd;
    
    /**
     * 创建人MIS账号
     */
    private String creatorMisId;
    
    /**
     * 被采集MIS账号列表
     */
    private List<String> collectedMisId;
    
    /**
     * 采样固定指标列表
     */
    private List<String> sampleFixedMetrics;
} 