package com.sankuai.dzusergrowth.operation.api.common;

import lombok.Data;

import java.util.List;

@Data
public class PageResult<T> {
    private List<T> list;
    private long total;
    private int pageNum;
    private int pageSize;

    public PageResult(List<T> list, long total, int pageNum, int pageSize) {
        this.list = list;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }


    /**
     * 获取list
     *
     * @return list
     */
    public List<T> getList() {
        return list;
    }

    /**
     * 获取total
     *
     * @return total
     */
    public long getTotal() {
        return total;
    }

    /**
     * 获取pageNum
     *
     * @return pageNum
     */
    public int getPageNum() {
        return pageNum;
    }

    /**
     * 获取pageSize
     *
     * @return pageSize
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 设置list
     *
     * @param list list
     */
    public void setList(List<T> list) {
        this.list = list;
    }

    /**
     * 设置total
     *
     * @param total total
     */
    public void setTotal(long total) {
        this.total = total;
    }

    /**
     * 设置pageNum
     *
     * @param pageNum pageNum
     */
    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 设置pageSize
     *
     * @param pageSize pageSize
     */
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static PageResultBuilder builder() {
        return new PageResultBuilder();
    }
    
    /**
     * Builder类
     */
    public static class PageResultBuilder {
        private List<T> list;
        private long total;
        private int pageNum;
        private int pageSize;
        
        public PageResultBuilder list(List<T> list) {
            this.list = list;
            return this;
        }
        public PageResultBuilder total(long total) {
            this.total = total;
            return this;
        }
        public PageResultBuilder pageNum(int pageNum) {
            this.pageNum = pageNum;
            return this;
        }
        public PageResultBuilder pageSize(int pageSize) {
            this.pageSize = pageSize;
            return this;
        }
        
        public PageResult build() {
            return new PageResult(list, total, pageNum, pageSize);
        }
    }
}
