package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据类型枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum DataTypeEnum {
    
    /**
     * 字符串类型
     */
    STRING(1, "string"),
    
    /**
     * 长整型
     */
    LONG(2, "long"),
    
    /**
     * 浮点型
     */
    FLOAT(3, "float");
    
    /**
     * 类型码
     */
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String description;
    
    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static DataTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DataTypeEnum dataType : values()) {
            if (dataType.getCode().equals(code)) {
                return dataType;
            }
        }
        return null;
    }
    
    /**
     * 检查类型码是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 