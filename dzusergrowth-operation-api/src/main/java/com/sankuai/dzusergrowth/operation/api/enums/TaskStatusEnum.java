package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {
    
    /**
     * 执行中
     */
    RUNNING(1, "执行中"),
    
    /**
     * 执行成功
     */
    SUCCESS(2, "执行成功"),
    
    /**
     * 执行失败
     */
    FAILED(3, "执行失败");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static TaskStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (TaskStatusEnum taskStatus : values()) {
            if (taskStatus.getCode().equals(code)) {
                return taskStatus;
            }
        }
        return null;
    }
    
    /**
     * 检查状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
    
    /**
     * 是否为执行中状态
     *
     * @return 是否为执行中
     */
    public boolean isRunning() {
        return this == RUNNING;
    }
    
    /**
     * 是否为成功状态
     *
     * @return 是否为成功
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 是否为失败状态
     *
     * @return 是否为失败
     */
    public boolean isFailed() {
        return this == FAILED;
    }
    
    /**
     * 是否为终态（成功或失败）
     *
     * @return 是否为终态
     */
    public boolean isFinished() {
        return this == SUCCESS || this == FAILED;
    }
} 