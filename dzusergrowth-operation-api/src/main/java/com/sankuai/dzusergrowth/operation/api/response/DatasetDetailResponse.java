package com.sankuai.dzusergrowth.operation.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 数据集详情响应
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetDetailResponse {
    
    /**
     * 数据集ID
     */
    private String datasetId;
    
    /**
     * 数据集名称
     */
    private String name;
    
    /**
     * 数据集描述
     */
    private String description;
    
    /**
     * 数据粒度
     */
    private String granularity; // ACTIVITY_AND_TASK, ASSET
    
    /**
     * 业务场景
     */
    private String businessScene;
    
    /**
     * 创建人MIS账号
     */
    private String creatorMisId;
    
    /**
     * 数据集内容
     * 动态字段集合，每项包含列名和对应的数据值
     */
    private List<Map<String, Object>> datasetContents;


    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public String getDatasetId() {
        return datasetId;
    }

    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取granularity
     *
     * @return granularity
     */
    public String getGranularity() {
        return granularity;
    }

    /**
     * 获取businessScene
     *
     * @return businessScene
     */
    public String getBusinessScene() {
        return businessScene;
    }

    /**
     * 获取creatorMisId
     *
     * @return creatorMisId
     */
    public String getCreatorMisId() {
        return creatorMisId;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置granularity
     *
     * @param granularity granularity
     */
    public void setGranularity(String granularity) {
        this.granularity = granularity;
    }

    /**
     * 设置businessScene
     *
     * @param businessScene businessScene
     */
    public void setBusinessScene(String businessScene) {
        this.businessScene = businessScene;
    }

    /**
     * 设置creatorMisId
     *
     * @param creatorMisId creatorMisId
     */
    public void setCreatorMisId(String creatorMisId) {
        this.creatorMisId = creatorMisId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetDetailResponseBuilder builder() {
        return new DatasetDetailResponseBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetDetailResponseBuilder {
        private String datasetId;
        private String name;
        private String description;
        private String granularity;
        private String businessScene;
        private String creatorMisId;
        
        public DatasetDetailResponseBuilder datasetId(String datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DatasetDetailResponseBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetDetailResponseBuilder description(String description) {
            this.description = description;
            return this;
        }
        public DatasetDetailResponseBuilder granularity(String granularity) {
            this.granularity = granularity;
            return this;
        }
        public DatasetDetailResponseBuilder businessScene(String businessScene) {
            this.businessScene = businessScene;
            return this;
        }
        public DatasetDetailResponseBuilder creatorMisId(String creatorMisId) {
            this.creatorMisId = creatorMisId;
            return this;
        }
        
        public DatasetDetailResponse build() {
            return new DatasetDetailResponse(datasetId, name, description, granularity, businessScene, creatorMisId);
        }
    }
} 