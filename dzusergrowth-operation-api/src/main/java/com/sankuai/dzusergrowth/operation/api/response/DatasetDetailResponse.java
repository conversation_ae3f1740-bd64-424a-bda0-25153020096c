package com.sankuai.dzusergrowth.operation.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 数据集详情响应
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetDetailResponse {
    
    /**
     * 数据集ID
     */
    private String datasetId;
    
    /**
     * 数据集名称
     */
    private String name;
    
    /**
     * 数据集描述
     */
    private String description;
    
    /**
     * 数据粒度
     */
    private String granularity; // ACTIVITY_AND_TASK, ASSET
    
    /**
     * 业务场景
     */
    private String businessScene;
    
    /**
     * 创建人MIS账号
     */
    private String creatorMisId;
    
    /**
     * 数据集内容
     * 动态字段集合，每项包含列名和对应的数据值
     */
    private List<Map<String, Object>> datasetContents;
} 