package com.sankuai.dzusergrowth.operation.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 数据集列表查询请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetListRequest {
    
    /**
     * 多条件过滤
     * 支持字段如数据集名称、创建者misId、粒度、业务场景等
     */
    private Map<String, Object> filters;
    
    /**
     * 页码（从1开始）
     */
    private Integer pageNo;
    
    /**
     * 每页条数
     */
    private Integer pageSize;
    
    /**
     * 获取数据集名称过滤条件
     */
    public String getNameFilter() {
        return filters != null ? (String) filters.get("name") : null;
    }
    
    /**
     * 获取创建者MIS账号过滤条件
     */
    public String getCreatorMisIdFilter() {
        return filters != null ? (String) filters.get("creatorMisId") : null;
    }
    
    /**
     * 获取粒度过滤条件
     */
    public String getGranularityFilter() {
        return filters != null ? (String) filters.get("granularity") : null;
    }
    
    /**
     * 获取业务场景过滤条件
     */
    public String getBusinessSceneFilter() {
        return filters != null ? (String) filters.get("businessScene") : null;
    }
} 