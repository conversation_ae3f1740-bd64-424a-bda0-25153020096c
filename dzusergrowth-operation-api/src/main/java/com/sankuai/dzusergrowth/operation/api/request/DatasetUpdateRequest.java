package com.sankuai.dzusergrowth.operation.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集更新请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetUpdateRequest {
    
    /**
     * 数据集唯一标识
     */
    private String datasetId;
    
    /**
     * 数据集名称（1-50字符）
     */
    private String name;
    
    /**
     * 数据集描述（0-200字符）
     */
    private String description;
} 