package com.sankuai.dzusergrowth.operation.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据集生成方式枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum GenerateTypeEnum {
    
    /**
     * 条件采集
     */
    CONDITION_SOURCE(1, "条件采集"),
    
    /**
     * 手动上传
     */
    MANUAL_UPLOAD(2, "手动上传");
    
    /**
     * 生成方式码
     */
    private final Integer code;
    
    /**
     * 生成方式描述
     */
    private final String description;
    
    /**
     * 根据生成方式码获取枚举
     *
     * @param code 生成方式码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static GenerateTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (GenerateTypeEnum generateType : values()) {
            if (generateType.getCode().equals(code)) {
                return generateType;
            }
        }
        return null;
    }
    
    /**
     * 检查生成方式码是否有效
     *
     * @param code 生成方式码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 