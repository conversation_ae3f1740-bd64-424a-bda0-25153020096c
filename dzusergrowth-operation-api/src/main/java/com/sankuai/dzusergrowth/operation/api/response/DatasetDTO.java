package com.sankuai.dzusergrowth.operation.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集DTO
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetDTO {
    
    /**
     * 数据集ID
     */
    private String datasetId;
    
    /**
     * 数据集名称
     */
    private String name;
    
    /**
     * 数据粒度
     */
    private String granularity; // ACTIVITY_AND_TASK, ASSET
    
    /**
     * 业务场景
     */
    private String businessScene;
    
    /**
     * 创建人MIS账号
     */
    private String creatorMisId;
    
    /**
     * 被采集MIS账号
     */
    private String collectedMisId;
    
    /**
     * 采样固定指标
     */
    private String sampleFixedMetrics;
    
    /**
     * 数据集描述
     */
    private String description;
} 