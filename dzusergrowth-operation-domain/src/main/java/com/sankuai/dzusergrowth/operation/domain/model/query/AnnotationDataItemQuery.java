package com.sankuai.dzusergrowth.operation.domain.model.query;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationDataItemStatusEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 标注数据条目查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationDataItemQuery {
    
    /**
     * 条目ID列表
     */
    private List<Long> itemIds;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据集ID列表
     */
    private List<Long> datasetIds;
    
    /**
     * 数据唯一键
     */
    private String dataUniqueKey;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 任务ID列表
     */
    private List<Long> taskIds;
    
    /**
     * 状态（数字码）
     */
    private Integer status;
    
    /**
     * 状态（枚举）
     */
    private AnnotationDataItemStatusEnum statusEnum;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
    
    /**
     * 设置状态枚举，同时设置对应的状态码
     */
    public void setStatusEnum(AnnotationDataItemStatusEnum statusEnum) {
        this.statusEnum = statusEnum;
        this.status = statusEnum != null ? statusEnum.getCode() : null;
    }
    
    /**
     * 获取有效的状态码（优先使用statusEnum的code）
     */
    public Integer getEffectiveStatus() {
        if (statusEnum != null) {
            return statusEnum.getCode();
        }
        return status;
    }
} 