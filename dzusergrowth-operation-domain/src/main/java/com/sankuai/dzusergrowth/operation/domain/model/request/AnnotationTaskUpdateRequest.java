package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationTaskTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationConfigDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标注任务更新请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskUpdateRequest {
    
    /**
     * 任务ID（必填）
     */
    private Long taskId;
    
    /**
     * 任务名称（可选更新）
     */
    private String taskName;
    
    /**
     * 标注类型（可选更新）
     */
    private AnnotationTaskTypeEnum annotationType;
    
    /**
     * 标注配置（可选更新）
     */
    private List<AnnotationConfigDO> annotationConfig;
    
    /**
     * 展示选用的列（可选更新）
     */
    private List<Long> showColumn;
    
    /**
     * 任务标注人员（可选更新）
     */
    private String annotator;
    
    /**
     * 任务描述（可选更新）
     */
    private String description;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 验证更新请求参数有效性
     */
    public boolean isValid() {
        // 必填字段验证
        if (taskId == null) {
            return false;
        }
        
        if (updaterId == null) {
            return false;
        }
        
        // 如果提供了任务名称，需要验证格式
        if (taskName != null && taskName.trim().isEmpty()) {
            return false;
        }

        return true;
    }
    
    /**
     * 检查是否有实际要更新的字段
     */
    public boolean hasFieldsToUpdate() {
        return taskName != null || 
               annotationType != null || 
               annotationConfig != null || 
               (showColumn != null && !showColumn.isEmpty()) || 
               annotator != null || 
               description != null;
    }
    
    /**
     * 验证任务名称格式（如果提供）
     */
    public boolean hasValidTaskName() {
        return taskName == null || (!taskName.trim().isEmpty() && taskName.length() <= 100);
    }
    
    /**
     * 验证标注配置的完整性（如果提供）
     */
    public boolean hasValidAnnotationConfig() {
        if (annotationConfig == null) {
            return true; // 不更新配置时认为有效
        }
        
        if (annotationConfig.isEmpty()) {
            return false; // 如果提供了配置但为空，则无效
        }
        
        // 检查是否有重复的标签ID
        long distinctLabelCount = annotationConfig.stream()
                .map(AnnotationConfigDO::getColumnId)
                .distinct()
                .count();
                
        return distinctLabelCount == annotationConfig.size();
    }
} 