package com.sankuai.dzusergrowth.operation.domain.service.impl;

import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationConfigDO;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationTaskDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnConfig;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetWDO;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskCreateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskDeleteRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.AnnotationTaskUpdateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnsBatchOperationRequest;
import com.sankuai.dzusergrowth.operation.domain.repository.AnnotationTaskRepository;
import com.sankuai.dzusergrowth.operation.domain.service.AnnotationDomainService;
import com.sankuai.dzusergrowth.operation.domain.service.DatasetDomainService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 标注域服务实现
 * 
 * <AUTHOR>
 * @date 2025/7/1 10:37
 */
@Service
public class AnnotationDomainServiceImpl implements AnnotationDomainService {

    @Resource
    private AnnotationTaskRepository annotationTaskRepository;

    @Resource
    private DatasetDomainService datasetDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationTaskDO createAnnotationTask(AnnotationTaskCreateRequest request) {
        // 1. 参数校验
        validateCreateAnnotationTaskRequest(request);
        
        // 2. 获取数据集聚合根并校验
        DatasetWDO datasetWDO = validateDatasetAggregateExistsAndSuccess(request.getAnnotationDatasetId());
        
        // 3. 校验展示列是否都在数据集中存在
        validateShowColumnsExist(datasetWDO, request.getShowColumn());
        
        // 4. 校验标注配置和列展示名不重复
        validateAnnotationConfigAndDisplayNames(request.getAnnotationConfig(), datasetWDO.getColumnInfoList());

        // 5. 为需要创建新列的标注配置创建数据集列，并填充columnId
        List<AnnotationConfigDO> completedAnnotationConfigs = createColumnsForAnnotationConfigs(
                request.getAnnotationDatasetId(), request.getAnnotationConfig(), request.getCreatorId());
        
        // 6. 用完整的标注配置创建标注任务
        AnnotationTaskDO annotationTaskDO = buildAnnotationTaskDOWithCompletedConfig(request, completedAnnotationConfigs);
        annotationTaskRepository.createAnnotationTask(annotationTaskDO);
        
        return annotationTaskDO;
    }

    @Override
    public AnnotationTaskDO updateAnnotationTask(AnnotationTaskUpdateRequest request) {
        // TODO: 实现更新标注任务逻辑
        throw new UnsupportedOperationException("updateAnnotationTask方法待实现");
    }

    @Override
    public void deleteAnnotationTask(AnnotationTaskDeleteRequest request) {
        // TODO: 实现删除标注任务逻辑
        throw new UnsupportedOperationException("deleteAnnotationTask方法待实现");
    }

    /**
     * 校验创建标注任务请求参数
     */
    private void validateCreateAnnotationTaskRequest(AnnotationTaskCreateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("创建标注任务请求不能为空");
        }
        
        if (request.getAnnotationDatasetId() == null) {
            throw new IllegalArgumentException("标注数据集ID不能为空");
        }
        
        if (request.getTaskName() == null || request.getTaskName().trim().isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        
        if (request.getAnnotationType() == null) {
            throw new IllegalArgumentException("标注类型不能为空");
        }
        
        if (request.getCreatorId() == null) {
            throw new IllegalArgumentException("创建人ID不能为空");
        }
        
        if (CollectionUtils.isEmpty(request.getAnnotationConfig())) {
            throw new IllegalArgumentException("标注配置不能为空");
        }
        
        // 验证标注配置的有效性
        for (AnnotationConfigDO config : request.getAnnotationConfig()) {
            if (config == null) {
                throw new IllegalArgumentException("标注配置项不能为空");
            }
            
            if (config.getLabelType() == null) {
                throw new IllegalArgumentException("标签类型不能为空");
            }
            
            if (config.getDisplayName() == null || config.getDisplayName().trim().isEmpty()) {
                throw new IllegalArgumentException("列展示名不能为空");
            }
            
            if (config.getIsRequired() == null) {
                throw new IllegalArgumentException("是否必选不能为空");
            }
            
            if (config.getIsMultiSelected() == null) {
                throw new IllegalArgumentException("是否多选不能为空");
            }
        }
    }

    /**
     * 获取数据集聚合根并校验是否存在且状态为成功
     */
    private DatasetWDO validateDatasetAggregateExistsAndSuccess(Long datasetId) {
        DatasetWDO datasetWDO = datasetDomainService.getDatasetAggregateById(datasetId);
        if (datasetWDO == null) {
            throw new IllegalStateException("数据集不存在，ID: " + datasetId);
        }
        
        if (datasetWDO.getTaskStatus() == null || !datasetWDO.getTaskStatus().isSuccess()) {
            String statusDesc = datasetWDO.getTaskStatus() != null ? 
                    datasetWDO.getTaskStatus().getDescription() : "未知状态";
            throw new IllegalStateException("数据集状态不是成功状态，当前状态: " + statusDesc);
        }
        
        return datasetWDO;
    }

    /**
     * 校验展示列是否都在数据集中存在
     */
    private void validateShowColumnsExist(DatasetWDO datasetWDO, List<Long> showColumnIds) {
        if (CollectionUtils.isEmpty(showColumnIds)) {
            return;
        }
        
        List<DatasetColumnInfoDO> columnInfoList = datasetWDO.getColumnInfoList();
        if (CollectionUtils.isEmpty(columnInfoList)) {
            throw new IllegalStateException("数据集中没有任何列信息，数据集ID: " + datasetWDO.getId());
        }
        
        // 获取数据集中存在的列ID集合
        Set<Long> existingColumnIds = columnInfoList.stream()
                .map(DatasetColumnInfoDO::getId)
                .collect(Collectors.toSet());
        
        // 检查展示列是否都存在
        for (Long showColumnId : showColumnIds) {
            if (!existingColumnIds.contains(showColumnId)) {
                throw new IllegalStateException("展示列不存在于数据集中，列ID: " + showColumnId + 
                        "，数据集ID: " + datasetWDO.getId());
            }
        }
    }

    /**
     * 校验标注配置和列展示名不重复
     */
    private void validateAnnotationConfigAndDisplayNames(List<AnnotationConfigDO> annotationConfigs, 
                                                       List<DatasetColumnInfoDO> datasetColumns) {
        if (CollectionUtils.isEmpty(annotationConfigs)) {
            throw new IllegalArgumentException("标注配置不能为空");
        }
        
        // 获取数据集中已有的列展示名
        Set<String> existingDisplayNames = datasetColumns.stream()
                .map(DatasetColumnInfoDO::getDisplayName)
                .filter(displayName -> displayName != null && !displayName.trim().isEmpty())
                .collect(Collectors.toSet());
        
        // 检查标注配置中的展示名是否重复
        for (AnnotationConfigDO config : annotationConfigs) {
            if (config.getDisplayName() == null || config.getDisplayName().trim().isEmpty()) {
                throw new IllegalArgumentException("标注配置的列展示名不能为空");
            }
            
            String displayName = config.getDisplayName().trim();
            if (existingDisplayNames.contains(displayName)) {
                throw new IllegalStateException("列展示名已存在: " + displayName);
            }
        }
        
        // 检查标注配置内部展示名是否重复
        Set<String> configDisplayNames = annotationConfigs.stream()
                .map(config -> config.getDisplayName().trim())
                .collect(Collectors.toSet());
        
        if (configDisplayNames.size() != annotationConfigs.size()) {
            throw new IllegalArgumentException("标注配置中存在重复的列展示名");
        }
    }

    /**
     * 为标注配置创建对应的数据集列，并填充columnId
     */
    private List<AnnotationConfigDO> createColumnsForAnnotationConfigs(Long datasetId, 
                                                                       List<AnnotationConfigDO> annotationConfigs, 
                                                                       Long creatorId) {
        // 构建需要创建的数据集列信息
        List<DatasetColumnInfoDO> columnsToCreate = new ArrayList<>();
        
        for (AnnotationConfigDO config : annotationConfigs) {
            if (config.getColumnId() == null) {
                // 构建需要创建的列信息
                DatasetColumnInfoDO columnInfoDO = DatasetColumnInfoDO.builder()
                        .datasetId(datasetId)
                        .name(generateColumnName(config.getDisplayName()))
                        .displayName(config.getDisplayName())
                        .dataType(determineDataType(config))
                        .columnType(ColumnTypeEnum.ANNOTATION_RESULT)
                        .columnConfig(buildColumnConfig(config))
                        .creatorId(creatorId)
                        .updaterId(creatorId)
                        .build();
                
                columnsToCreate.add(columnInfoDO);
            }
        }
        
        // 批量创建数据集列
        if (!columnsToCreate.isEmpty()) {
            DatasetColumnsBatchOperationRequest batchRequest = DatasetColumnsBatchOperationRequest.builder()
                    .datasetId(datasetId)
                    .columns(columnsToCreate)
                    .operatorId(creatorId)
                    .build();
            
            datasetDomainService.batchOperateDatasetColumns(batchRequest);
        }
        
        // 构建完整的标注配置列表
        List<AnnotationConfigDO> completedConfigs = new ArrayList<>();
        int createdColumnIndex = 0;
        
        for (AnnotationConfigDO config : annotationConfigs) {
            if (config.getColumnId() == null) {
                // 使用创建后的列ID（已经通过引用更新）
                DatasetColumnInfoDO createdColumn = columnsToCreate.get(createdColumnIndex);
                AnnotationConfigDO completedConfig = AnnotationConfigDO.builder()
                        .columnId(createdColumn.getId())
                        .labelType(config.getLabelType())
                        .labelOption(config.getLabelOption())
                        .isMultiSelected(config.getIsMultiSelected())
                        .isRequired(config.getIsRequired())
                        .displayName(config.getDisplayName())
                        .build();
                
                completedConfigs.add(completedConfig);
                createdColumnIndex++;
            } else {
                // 已有columnId，直接使用原配置
                completedConfigs.add(config);
            }
        }
        
        return completedConfigs;
    }
    
    /**
     * 用完整的标注配置构建标注任务DO对象
     */
    private AnnotationTaskDO buildAnnotationTaskDOWithCompletedConfig(AnnotationTaskCreateRequest request, 
                                                                      List<AnnotationConfigDO> completedAnnotationConfigs) {
        return AnnotationTaskDO.builder()
                .annotationDatasetId(request.getAnnotationDatasetId())
                .taskName(request.getTaskName())
                .annotationTaskType(request.getAnnotationType())
                .annotationConfig(completedAnnotationConfigs)  // 使用完整的配置
                .showColumn(request.getShowColumn())
                .annotator(request.getAnnotator())
                .description(request.getDescription())
                .creatorId(request.getCreatorId())
                .updaterId(request.getCreatorId())
                .build();
    }



    /**
     * 生成列名（基于展示名）
     */
    private String generateColumnName(String displayName) {
        // 简单的命名规则：将展示名转换为列名
        // 可以根据实际需求调整命名规则
        return "annotation_" + displayName.toLowerCase().replaceAll("[^a-zA-Z0-9_]", "_");
    }

    /**
     * 根据标注配置确定数据类型
     */
    private DataTypeEnum determineDataType(AnnotationConfigDO config) {
        // 根据标注标签类型确定数据类型
        // 这里简化处理，实际可以根据labelType做更精确的判断
        if (config.getLabelType() != null) {
            switch (config.getLabelType()) {
                case TEXT:
                case ENUM:
                    return DataTypeEnum.STRING;
                default:
                    return DataTypeEnum.STRING;
            }
        }
        return DataTypeEnum.STRING;
    }

    /**
     * 构建列配置对象
     */
    private DatasetColumnConfig buildColumnConfig(AnnotationConfigDO config) {
        // 根据标注配置构建列配置
        return DatasetColumnConfig.builder()
                .labelType(config.getLabelType())
                .labelOption(config.getLabelOption())
                .labelIsMultiSelected(config.getIsMultiSelected())
                .labelIsRequired(config.getIsRequired())
                .build();
    }
}
