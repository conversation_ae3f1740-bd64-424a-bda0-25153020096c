package com.sankuai.dzusergrowth.operation.domain.model.annotation;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationDataItemStatusEnum;
import lombok.*;

import java.util.Date;

/**
 * 标注数据条目领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationDataItemDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据唯一键-标识: 比如TraceID，活动ID等
     */
    private String dataUniqueKey;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 状态
     */
    private AnnotationDataItemStatusEnum status;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证数据唯一键不为空
     */
    public boolean isValidDataUniqueKey() {
        return dataUniqueKey != null && !dataUniqueKey.trim().isEmpty();
    }
    
    /**
     * 验证状态是否有效
     */
    public boolean isValidStatus() {
        return status != null;
    }
    
    /**
     * 检查是否为未标注状态
     */
    public boolean isUnAnnotated() {
        return AnnotationDataItemStatusEnum.UN_ANNOTATED.equals(status);
    }
    
    /**
     * 检查是否为标注中状态
     */
    public boolean isAnnotating() {
        return AnnotationDataItemStatusEnum.ANNOTATING.equals(status);
    }
    
    /**
     * 检查是否为已标注状态
     */
    public boolean isAnnotated() {
        return AnnotationDataItemStatusEnum.ANNOTATED.equals(status);
    }
} 