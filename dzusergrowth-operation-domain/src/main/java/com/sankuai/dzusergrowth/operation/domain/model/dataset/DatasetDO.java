package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import lombok.*;

import java.util.Date;

/**
 * 数据集领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetDO {
    
    /**
     * 数据集ID
     */
    private Long id;
    
    /**
     * 数据集类型
     */
    private DatasetTypeEnum datasetType;
    
    /**
     * 数据粒度
     */
    private DataGranularityEnum dataGranularity;
    
    /**
     * 数据集名称
     */
    private String name;
    
    /**
     * 数据集生成方式
     */
    private GenerateTypeEnum generateType;
    
    /**
     * 生成配置对象
     * 在存储时会转换为JSON字符串
     */
    private DatasetGenerateConfig generateConfig;
    
    /**
     * 任务状态
     */
    private TaskStatusEnum taskStatus;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 数据集描述
     */
    private String description;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证数据集名称不为空
     */
    public boolean isValidName() {
        return name != null && !name.trim().isEmpty();
    }
    
    /**
     * 验证数据集类型是否有效
     */
    public boolean isValidDatasetType() {
        return datasetType != null;
    }
    
    /**
     * 验证数据粒度是否有效
     */
    public boolean isValidDataGranularity() {
        return dataGranularity != null;
    }
    
    /**
     * 验证生成方式是否有效
     */
    public boolean isValidGenerateType() {
        return generateType != null;
    }
    
    /**
     * 验证生成配置是否有效
     */
    public boolean isValidGenerateConfig() {
        return generateConfig != null && generateConfig.isValid();
    }
    
    /**
     * 从生成配置获取活动类型
     */
    public Integer getActivityType() {
        return generateConfig != null ? generateConfig.getActivityTypeCode() : null;
    }
    
    /**
     * 从生成配置获取资产类型
     */
    public Integer getAssetType() {
        return generateConfig != null ? generateConfig.getAssetTypeCode() : null;
    }
    
    /**
     * 从生成配置获取活动类型文本
     */
    public String getActivityTypeText() {
        return generateConfig != null ? generateConfig.getActivityTypeText() : "未知";
    }
    
    /**
     * 从生成配置获取资产类型文本
     */
    public String getAssetTypeText() {
        return generateConfig != null ? generateConfig.getAssetTypeText() : "未知";
    }
    
    /**
     * 从生成配置获取采集开始时间
     */
    public Date getCollectionStartTime() {
        return generateConfig != null ? generateConfig.getStartTime() : null;
    }
    
    /**
     * 从生成配置获取采集结束时间
     */
    public Date getCollectionEndTime() {
        return generateConfig != null ? generateConfig.getEndTime() : null;
    }
    
    /**
     * 验证任务状态是否有效
     */
    public boolean isValidTaskStatus() {
        return taskStatus != null;
    }
    
    /**
     * 是否为执行中状态
     */
    public boolean isTaskRunning() {
        return taskStatus != null && taskStatus.isRunning();
    }
    
    /**
     * 是否为执行成功状态
     */
    public boolean isTaskSuccess() {
        return taskStatus != null && taskStatus.isSuccess();
    }
    
    /**
     * 是否为执行失败状态
     */
    public boolean isTaskFailed() {
        return taskStatus != null && taskStatus.isFailed();
    }
    
    /**
     * 任务是否已完成（成功或失败）
     */
    public boolean isTaskFinished() {
        return taskStatus != null && taskStatus.isFinished();
    }
} 