package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import lombok.*;

import java.util.Date;
import java.util.Map;

/**
 * 数据条目领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataItemDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据唯一键-标识: 比如TraceID，活动ID等
     */
    private String dataUniqueKey;

    
    /**
     * 数据内容映射
     * key: 列ID，value: 列的数据值
     * 在存储时会转换为JSON字符串
     */
    private Map<Long, String> data;

    
    /**
     * 软删除标记
     */
    private Boolean isDeleted;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证数据唯一键不为空
     */
    public boolean isValidDataUniqueKey() {
        return dataUniqueKey != null && !dataUniqueKey.trim().isEmpty();
    }
    
    /**
     * 验证数据内容不为空
     */
    public boolean isValidData() {
        return data != null && !data.isEmpty();
    }
    
    /**
     * 检查是否已删除
     */
    public boolean isDeleted() {
        return Boolean.TRUE.equals(isDeleted);
    }
    
    /**
     * 标记为删除
     */
    public void markAsDeleted() {
        this.isDeleted = true;
    }
    
    /**
     * 根据列ID获取数据值
     * 
     * @param columnId 列ID
     * @return 对应的数据值，如果不存在则返回null
     */
    public String getDataByColumnId(Long columnId) {
        if (data == null || columnId == null) {
            return null;
        }
        return data.get(columnId);
    }

    /**
     * 设置指定列的数据值
     * 
     * @param columnId 列ID
     * @param value 数据值
     */
    public void setDataByColumnId(Long columnId, String value) {
        if (data == null) {
            data = new java.util.HashMap<>();
        }
        data.put(columnId, value);
    }
    
    /**
     * 移除指定列的数据
     * 
     * @param columnId 列ID
     * @return 被移除的数据值
     */
    public String removeDataByColumnId(Long columnId) {
        if (data == null || columnId == null) {
            return null;
        }
        return data.remove(columnId);
    }
} 