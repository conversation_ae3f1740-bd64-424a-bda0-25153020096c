package com.sankuai.dzusergrowth.operation.domain.model.evaluation;

import com.sankuai.dzusergrowth.operation.api.enums.EvaluationTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import lombok.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 评测任务领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationTaskDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 资源测的数据集ID
     */
    private Long sourceDatasetId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 评测类型: MANUAL-手动评测; AUTO-自动检验
     */
    private EvaluationTypeEnum evaluationType;
    
    /**
     * 评测指标计算配置列表
     */
    private List<EvaluationIndicatorConfig> evaluationConfig;
    
    /**
     * 产出数据集ID
     */
    private Long evaluationDatasetId;
    
    /**
     * 任务执行状态
     */
    private TaskStatusEnum status;
    
    /**
     * 统计结果
     */
    private Map<String, Object> evaluationData;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证任务名称不为空
     */
    public boolean isValidTaskName() {
        return taskName != null && !taskName.trim().isEmpty();
    }
    
    /**
     * 验证评测类型是否有效
     */
    public boolean isValidEvaluationType() {
        return evaluationType != null;
    }
    
    /**
     * 是否为手动评测
     */
    public boolean isManualEvaluation() {
        return evaluationType != null && evaluationType.isManual();
    }
    
    /**
     * 是否为自动检验
     */
    public boolean isAutoEvaluation() {
        return evaluationType != null && evaluationType.isAuto();
    }
    
    /**
     * 验证评测配置是否有效
     */
    public boolean isValidEvaluationConfig() {
        return evaluationConfig != null && !evaluationConfig.isEmpty() &&
               evaluationConfig.stream().allMatch(EvaluationIndicatorConfig::isValid);
    }
    
    /**
     * 验证状态是否有效
     */
    public boolean isValidStatus() {
        return status != null;
    }
    
    /**
     * 是否为执行中状态
     */
    public boolean isRunningStatus() {
        return status != null && status.isRunning();
    }
    
    /**
     * 是否为成功状态
     */
    public boolean isSuccessStatus() {
        return status != null && status.isSuccess();
    }
    
    /**
     * 是否为失败状态
     */
    public boolean isFailedStatus() {
        return status != null && status.isFailed();
    }
    
    /**
     * 任务是否已完成（成功或失败）
     */
    public boolean isFinished() {
        return status != null && status.isFinished();
    }
} 