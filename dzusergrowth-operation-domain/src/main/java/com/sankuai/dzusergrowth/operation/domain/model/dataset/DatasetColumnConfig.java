package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationLabelTypeEnum;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.*;

import java.util.List;

/**
 * 数据集列配置对象
 * 对应数据集列信息表中config字段的JSON结构
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnConfig {
    
    /**
     * 固定指标：采集指标ID
     */
    private String collectId;
    
    /**
     * 标注类型: 1-枚举; 2-文本
     */
    @com.fasterxml.jackson.annotation.JsonIgnore
    private AnnotationLabelTypeEnum labelType;
    
    /**
     * 枚举选项（仅当labelType=1时有效）
     */
    private List<String> labelOption;
    
    /**
     * 是否多选
     */
    private Boolean labelIsMultiSelected;
    
    /**
     * 是否必选
     */
    private Boolean labelIsRequired;
    
    /**
     * JSON序列化时将标注类型枚举转换为code值
     */
    @JsonGetter("labelType")
    public Integer getLabelTypeForJson() {
        return labelType != null ? labelType.getCode() : null;
    }
    
    /**
     * JSON反序列化时将code值转换为标注类型枚举
     */
    @JsonSetter("labelType")
    public void setLabelTypeFromJson(Integer code) {
        this.labelType = AnnotationLabelTypeEnum.fromCode(code);
    }
    
    /**
     * 获取标注类型编码（兼容性方法）
     */
    public Integer getLabelTypeCode() {
        return labelType != null ? labelType.getCode() : null;
    }
    
    /**
     * 获取标注类型文本描述
     */
    public String getLabelTypeText() {
        return labelType != null ? labelType.getDescription() : "未知";
    }
} 