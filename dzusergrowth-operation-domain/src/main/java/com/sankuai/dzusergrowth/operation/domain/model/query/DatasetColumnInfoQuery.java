package com.sankuai.dzusergrowth.operation.domain.model.query;

import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 数据集列信息查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnInfoQuery {
    
    /**
     * 列ID列表
     */
    private List<Long> columnIds;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据集ID列表
     */
    private List<Long> datasetIds;
    
    /**
     * 列名（模糊查询）
     */
    private String name;
    
    /**
     * 数据类型: 1-string; 2-long; 3-float
     */
    private Integer dataType;
    
    /**
     * 列类型: 1-生成数据; 2-标注结果; 3-统计指标; 4-大模型评测指标
     */
    private Integer columnType;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
    
    /**
     * 通过数据类型枚举设置查询条件
     *
     * @param dataTypeEnum 数据类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetColumnInfoQuery setDataType(DataTypeEnum dataTypeEnum) {
        this.dataType = dataTypeEnum != null ? dataTypeEnum.getCode() : null;
        return this;
    }
    
    /**
     * 通过列类型枚举设置查询条件
     *
     * @param columnTypeEnum 列类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetColumnInfoQuery setColumnType(ColumnTypeEnum columnTypeEnum) {
        this.columnType = columnTypeEnum != null ? columnTypeEnum.getCode() : null;
        return this;
    }
} 