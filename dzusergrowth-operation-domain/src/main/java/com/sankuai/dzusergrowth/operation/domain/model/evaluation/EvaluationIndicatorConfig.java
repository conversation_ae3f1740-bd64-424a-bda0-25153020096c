package com.sankuai.dzusergrowth.operation.domain.model.evaluation;

import lombok.*;

/**
 * 评测指标计算配置
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationIndicatorConfig {
    
    /**
     * 列ID（从数据集列信息表中映射得到）
     */
    private Long columnId;
    
    /**
     * 计算方式
     */
    private String calculationMethod;
    
    /**
     * 列展示名
     */
    private String displayName;
    
    /**
     * 验证配置是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return columnId != null && 
               calculationMethod != null && !calculationMethod.trim().isEmpty() &&
               displayName != null && !displayName.trim().isEmpty();
    }
} 