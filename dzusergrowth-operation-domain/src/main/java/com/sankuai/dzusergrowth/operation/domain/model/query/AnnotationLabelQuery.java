package com.sankuai.dzusergrowth.operation.domain.model.query;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 标注标签查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationLabelQuery {
    
    /**
     * 标签ID列表
     */
    private List<Long> labelIds;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
} 