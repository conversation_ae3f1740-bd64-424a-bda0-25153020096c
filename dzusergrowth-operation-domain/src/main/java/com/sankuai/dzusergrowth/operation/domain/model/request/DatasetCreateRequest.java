package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetGenerateConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集创建请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetCreateRequest {
    
    /**
     * 数据集类型
     */
    private DatasetTypeEnum datasetType;
    
    /**
     * 数据粒度
     */
    private DataGranularityEnum dataGranularity;
    
    /**
     * 数据集名称
     */
    private String name;
    
    /**
     * 数据集生成方式
     */
    private GenerateTypeEnum generateType;
    
    /**
     * 生成配置对象
     */
    private DatasetGenerateConfig generateConfig;
    
    /**
     * 数据集描述
     */
    private String description;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 验证创建请求参数有效性
     */
    public boolean isValid() {
        // 基础字段验证
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        if (datasetType == null) {
            return false;
        }
        
        if (dataGranularity == null) {
            return false;
        }
        
        if (generateType == null) {
            return false;
        }
        
        if (creatorId == null) {
            return false;
        }
        
        // 生成配置验证
        if (generateConfig == null || !generateConfig.isValid()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证数据集名称格式
     */
    public boolean hasValidName() {
        return name != null && !name.trim().isEmpty() && name.length() <= 100;
    }
} 