package com.sankuai.dzusergrowth.operation.domain.model.query;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationTaskTypeEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 标注任务查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskQuery {
    
    /**
     * 任务ID列表
     */
    private List<Long> taskIds;
    
    /**
     * 标注数据的数据集ID
     */
    private Long annotationDatasetId;
    
    /**
     * 任务名称（模糊查询）
     */
    private String taskName;
    
    /**
     * 标注类型（数字码）
     */
    private Integer annotationType;
    
    /**
     * 标注类型（枚举）
     */
    private AnnotationTaskTypeEnum annotationTaskTypeEnum;
    
    /**
     * 任务标注人员
     */
    private String annotator;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
    
    /**
     * 设置标注类型枚举，同时设置对应的类型码
     */
    public void setAnnotationTaskTypeEnum(AnnotationTaskTypeEnum annotationTaskTypeEnum) {
        this.annotationTaskTypeEnum = annotationTaskTypeEnum;
        this.annotationType = annotationTaskTypeEnum != null ? annotationTaskTypeEnum.getCode() : null;
    }
    
    /**
     * 获取有效的标注类型码（优先使用annotationTypeEnum的code）
     */
    public Integer getEffectiveAnnotationType() {
        if (annotationTaskTypeEnum != null) {
            return annotationTaskTypeEnum.getCode();
        }
        return annotationType;
    }
} 