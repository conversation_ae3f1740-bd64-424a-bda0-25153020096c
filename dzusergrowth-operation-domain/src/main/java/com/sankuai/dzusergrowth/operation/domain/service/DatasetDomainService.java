package com.sankuai.dzusergrowth.operation.domain.service;

import com.sankuai.dzusergrowth.common.api.response.PageResult;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetWDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DataItemDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetQuery;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetCreateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetUpdateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnsBatchOperationRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnCreateRequest;

import java.util.List;

/**
 * 数据集域服务
 * 
 * <AUTHOR> Assistant
 */
public interface DatasetDomainService {

    /**
     * 根据数据集ID获取数据集聚合根
     * 
     * @param datasetId 数据集ID
     * @return 数据集聚合根，如不存在则返回null
     * @throws IllegalArgumentException 当数据集ID为空或无效时抛出
     */
    DatasetWDO getDatasetAggregateById(Long datasetId);

    /**
     * 根据数据集ID获取数据集基本信息
     * 
     * @param datasetId 数据集ID
     * @return 数据集基本信息，如不存在则返回null
     * @throws IllegalArgumentException 当数据集ID为空或无效时抛出
     */
    DatasetDO getDatasetById(Long datasetId);

    /**
     * 创建数据集
     * 
     * @param request 数据集创建请求
     * @return 创建后的数据集DO
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws IllegalStateException 当数据集名称已存在或其他业务规则冲突时抛出
     */
    DatasetDO createDataset(DatasetCreateRequest request);
    
    /**
     * 更新数据集
     * 
     * @param request 数据集更新请求
     * @return 更新后的数据集DO
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws IllegalStateException 当数据集不存在或无法更新时抛出
     */
    DatasetDO updateDataset(DatasetUpdateRequest request);

    /**
     * 根据查询条件分页获取数据集列表
     * 
     * @param query 数据集查询条件（包含分页参数）
     * @return 分页结果，包含数据集DO列表和分页信息
     * @throws IllegalArgumentException 当查询条件无效时抛出
     */
    PageResult<DatasetDO> queryDatasetsWithPagination(DatasetQuery query);

    /**
     * 创建数据集列信息
     * 
     * @param request 数据集列创建请求
     * @return 创建后的数据集列信息DO
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws IllegalStateException 当数据集不存在或列名已存在时抛出
     */
    DatasetColumnInfoDO createDatasetColumn(DatasetColumnCreateRequest request);

    /**
     * 根据数据集ID获取该数据集的所有列信息
     * 
     * @param datasetId 数据集ID
     * @return 数据集列信息列表，按创建时间升序排列
     * @throws IllegalArgumentException 当数据集ID为空或无效时抛出
     */
    List<DatasetColumnInfoDO> getDatasetColumnsByDatasetId(Long datasetId);

    /**
     * 根据数据集ID分页查询数据条目
     * 
     * @param datasetId 数据集ID
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页结果，包含数据条目列表和分页信息
     * @throws IllegalArgumentException 当数据集ID为空或分页参数无效时抛出
     */
    PageResult<DataItemDO> queryDataItemsByDatasetIdWithPagination(Long datasetId, Integer pageNum, Integer pageSize);

    /**
     * 批量操作数据集列
     *
     * @param request 数据集列批量操作请求，包含数据集ID和列信息列表
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws IllegalStateException 当数据集不存在或列操作失败时抛出
     */
    void batchOperateDatasetColumns(DatasetColumnsBatchOperationRequest request);

} 