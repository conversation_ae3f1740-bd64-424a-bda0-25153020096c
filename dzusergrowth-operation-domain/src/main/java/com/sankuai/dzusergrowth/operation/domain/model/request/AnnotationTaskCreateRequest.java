package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationTaskTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationConfigDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标注任务创建请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskCreateRequest {
    
    /**
     * 标注数据的数据集ID
     */
    private Long annotationDatasetId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 标注类型
     */
    private AnnotationTaskTypeEnum annotationType;
    
    /**
     * 标注配置
     */
    private List<AnnotationConfigDO> annotationConfig;
    
    /**
     * 展示选用的列
     */
    private List<Long> showColumn;
    
    /**
     * 任务标注人员
     */
    private String annotator;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 验证创建请求参数有效性
     */
    public boolean isValid() {
        // 基础字段验证
        if (taskName == null || taskName.trim().isEmpty()) {
            return false;
        }
        
        if (annotationType == null) {
            return false;
        }
        
        if (creatorId == null) {
            return false;
        }
        
        if (annotationDatasetId == null) {
            return false;
        }
        
        // 标注配置验证
        if (annotationConfig == null || annotationConfig.isEmpty()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证任务名称是否重复（业务逻辑验证）
     */
    public boolean hasValidTaskName() {
        return taskName != null && !taskName.trim().isEmpty() && taskName.length() <= 100;
    }
    
    /**
     * 验证标注配置的完整性
     */
    public boolean hasValidAnnotationConfig() {
        if (annotationConfig == null || annotationConfig.isEmpty()) {
            return false;
        }
        
        // 检查是否有重复的标签ID
        long distinctLabelCount = annotationConfig.stream()
                .map(AnnotationConfigDO::getLabelId)
                .distinct()
                .count();
                
        return distinctLabelCount == annotationConfig.size();
    }
} 