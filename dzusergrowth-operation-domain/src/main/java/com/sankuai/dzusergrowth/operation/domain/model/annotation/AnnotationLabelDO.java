package com.sankuai.dzusergrowth.operation.domain.model.annotation;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 标注标签领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationLabelDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 标签配置（选项列表）
     */
    private List<String> labelConfig;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证标签配置不为空
     */
    public boolean isValidLabelConfig() {
        return labelConfig != null && !labelConfig.isEmpty() && 
               labelConfig.stream().allMatch(config -> config != null && !config.trim().isEmpty());
    }
    
    /**
     * 获取配置选项数量
     */
    public int getConfigOptionCount() {
        return labelConfig != null ? labelConfig.size() : 0;
    }
    
    /**
     * 检查是否包含指定选项
     */
    public boolean containsOption(String option) {
        return labelConfig != null && labelConfig.contains(option);
    }
} 