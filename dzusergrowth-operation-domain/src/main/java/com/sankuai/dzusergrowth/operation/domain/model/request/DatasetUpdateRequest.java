package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetGenerateConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集更新请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetUpdateRequest {
    
    /**
     * 数据集ID（必填）
     */
    private Long id;
    
    /**
     * 数据集类型（可选更新）
     */
    private DatasetTypeEnum datasetType;
    
    /**
     * 数据粒度（可选更新）
     */
    private DataGranularityEnum dataGranularity;
    
    /**
     * 数据集名称（可选更新）
     */
    private String name;
    
    /**
     * 数据集生成方式（可选更新）
     */
    private GenerateTypeEnum generateType;
    
    /**
     * 生成配置对象（可选更新）
     */
    private DatasetGenerateConfig generateConfig;
    
    /**
     * 数据集描述（可选更新）
     */
    private String description;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 验证更新请求参数有效性
     */
    public boolean isValid() {
        // 必填字段验证
        if (id == null) {
            return false;
        }
        
        if (updaterId == null) {
            return false;
        }
        
        // 如果提供了名称，需要验证格式
        if (name != null && name.trim().isEmpty()) {
            return false;
        }
        
        // 如果提供了生成配置，需要验证有效性
        if (generateConfig != null && !generateConfig.isValid()) {
            return false;
        }

        return true;
    }
    
    /**
     * 检查是否有实际要更新的字段
     */
    public boolean hasFieldsToUpdate() {
        return datasetType != null || 
               dataGranularity != null || 
               name != null || 
               generateType != null || 
               generateConfig != null || 
               description != null;
    }
    
    /**
     * 验证数据集名称格式（如果提供）
     */
    public boolean hasValidName() {
        return name == null || (!name.trim().isEmpty() && name.length() <= 100);
    }
} 