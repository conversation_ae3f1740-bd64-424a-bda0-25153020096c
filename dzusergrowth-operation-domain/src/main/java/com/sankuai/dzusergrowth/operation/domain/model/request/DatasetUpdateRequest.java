package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetGenerateConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集更新请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetUpdateRequest {
    
    /**
     * 数据集ID（必填）
     */
    private Long datasetId;
    
    /**
     * 数据集类型（可选更新）
     */
    private DatasetTypeEnum datasetType;
    
    /**
     * 数据粒度（可选更新）
     */
    private DataGranularityEnum dataGranularity;
    
    /**
     * 数据集名称（可选更新）
     */
    private String name;
    
    /**
     * 数据集生成方式（可选更新）
     */
    private GenerateTypeEnum generateType;
    
    /**
     * 生成配置对象（可选更新）
     */
    private DatasetGenerateConfig generateConfig;
    
    /**
     * 数据集描述（可选更新）
     */
    private String description;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 验证更新请求参数有效性
     */
    public boolean isValid() {
        // 必填字段验证
        if (id == null) {
            return false;
        }
        
        if (updaterId == null) {
            return false;
        }
        
        // 如果提供了名称，需要验证格式
        if (name != null && name.trim().isEmpty()) {
            return false;
        }
        
        // 如果提供了生成配置，需要验证有效性
        if (generateConfig != null && !generateConfig.isValid()) {
            return false;
        }

        return true;
    }
    
    /**
     * 检查是否有实际要更新的字段
     */
    public boolean hasFieldsToUpdate() {
        return datasetType != null || 
               dataGranularity != null || 
               name != null || 
               generateType != null || 
               generateConfig != null || 
               description != null;
    }
    
    /**
     * 验证数据集名称格式（如果提供）
     */
    public boolean hasValidName() {
        return name == null || (!name.trim().isEmpty() && name.length() <= 100);
    }


    /**
     * 获取datasetId
     *
     * @return datasetId
     */
    public Long getDatasetId() {
        return datasetId;
    }

    /**
     * 获取datasetType
     *
     * @return datasetType
     */
    public DatasetTypeEnum getDatasetType() {
        return datasetType;
    }

    /**
     * 获取dataGranularity
     *
     * @return dataGranularity
     */
    public DataGranularityEnum getDataGranularity() {
        return dataGranularity;
    }

    /**
     * 获取name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * 获取generateType
     *
     * @return generateType
     */
    public GenerateTypeEnum getGenerateType() {
        return generateType;
    }

    /**
     * 获取generateConfig
     *
     * @return generateConfig
     */
    public DatasetGenerateConfig getGenerateConfig() {
        return generateConfig;
    }

    /**
     * 获取description
     *
     * @return description
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取updaterId
     *
     * @return updaterId
     */
    public Long getUpdaterId() {
        return updaterId;
    }

    /**
     * 设置datasetId
     *
     * @param datasetId datasetId
     */
    public void setDatasetId(Long datasetId) {
        this.datasetId = datasetId;
    }

    /**
     * 设置datasetType
     *
     * @param datasetType datasetType
     */
    public void setDatasetType(DatasetTypeEnum datasetType) {
        this.datasetType = datasetType;
    }

    /**
     * 设置dataGranularity
     *
     * @param dataGranularity dataGranularity
     */
    public void setDataGranularity(DataGranularityEnum dataGranularity) {
        this.dataGranularity = dataGranularity;
    }

    /**
     * 设置name
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置generateType
     *
     * @param generateType generateType
     */
    public void setGenerateType(GenerateTypeEnum generateType) {
        this.generateType = generateType;
    }

    /**
     * 设置generateConfig
     *
     * @param generateConfig generateConfig
     */
    public void setGenerateConfig(DatasetGenerateConfig generateConfig) {
        this.generateConfig = generateConfig;
    }

    /**
     * 设置description
     *
     * @param description description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 设置updaterId
     *
     * @param updaterId updaterId
     */
    public void setUpdaterId(Long updaterId) {
        this.updaterId = updaterId;
    }

    /**
     * 创建Builder
     *
     * @return Builder实例
     */
    public static DatasetUpdateRequestBuilder builder() {
        return new DatasetUpdateRequestBuilder();
    }
    
    /**
     * Builder类
     */
    public static class DatasetUpdateRequestBuilder {
        private Long datasetId;
        private DatasetTypeEnum datasetType;
        private DataGranularityEnum dataGranularity;
        private String name;
        private GenerateTypeEnum generateType;
        private DatasetGenerateConfig generateConfig;
        private String description;
        private Long updaterId;
        
        public DatasetUpdateRequestBuilder datasetId(Long datasetId) {
            this.datasetId = datasetId;
            return this;
        }
        public DatasetUpdateRequestBuilder datasetType(DatasetTypeEnum datasetType) {
            this.datasetType = datasetType;
            return this;
        }
        public DatasetUpdateRequestBuilder dataGranularity(DataGranularityEnum dataGranularity) {
            this.dataGranularity = dataGranularity;
            return this;
        }
        public DatasetUpdateRequestBuilder name(String name) {
            this.name = name;
            return this;
        }
        public DatasetUpdateRequestBuilder generateType(GenerateTypeEnum generateType) {
            this.generateType = generateType;
            return this;
        }
        public DatasetUpdateRequestBuilder generateConfig(DatasetGenerateConfig generateConfig) {
            this.generateConfig = generateConfig;
            return this;
        }
        public DatasetUpdateRequestBuilder description(String description) {
            this.description = description;
            return this;
        }
        public DatasetUpdateRequestBuilder updaterId(Long updaterId) {
            this.updaterId = updaterId;
            return this;
        }
        
        public DatasetUpdateRequest build() {
            return new DatasetUpdateRequest(datasetId, datasetType, dataGranularity, name, generateType, generateConfig, description, updaterId);
        }
    }
} 