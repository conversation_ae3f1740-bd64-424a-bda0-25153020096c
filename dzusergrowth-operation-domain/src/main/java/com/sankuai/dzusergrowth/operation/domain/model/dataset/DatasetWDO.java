package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 数据集聚合根
 * 包含数据集基本信息和相关的列信息
 * 
 * <AUTHOR>
 * @date 2025/7/1 10:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetWDO {
    
    /**
     * 数据集ID
     */
    private Long id;

    /**
     * 数据集类型
     */
    private DatasetTypeEnum datasetType;

    /**
     * 数据粒度
     */
    private DataGranularityEnum dataGranularity;

    /**
     * 数据集名称
     */
    private String name;

    /**
     * 任务状态
     */
    private TaskStatusEnum taskStatus;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 数据集描述
     */
    private String description;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 全量列信息
     */
    private List<DatasetColumnInfoDO> columnInfoList;
    
    /**
     * 根据DatasetDO构建聚合根的静态工厂方法
     * 
     * @param datasetDO 数据集DO
     * @param columnInfoList 列信息列表
     * @return 数据集聚合根
     */
    public static DatasetWDO buildFromDatasetDO(DatasetDO datasetDO, List<DatasetColumnInfoDO> columnInfoList) {
        if (datasetDO == null) {
            return null;
        }
        
        return DatasetWDO.builder()
                .id(datasetDO.getId())
                .datasetType(datasetDO.getDatasetType())
                .dataGranularity(datasetDO.getDataGranularity())
                .name(datasetDO.getName())
                .taskStatus(datasetDO.getTaskStatus())
                .creatorId(datasetDO.getCreatorId())
                .description(datasetDO.getDescription())
                .addTime(datasetDO.getAddTime())
                .updateTime(datasetDO.getUpdateTime())
                .columnInfoList(columnInfoList)
                .build();
    }
    
    /**
     * 获取列信息数量
     * 
     * @return 列信息数量
     */
    public int getColumnCount() {
        return columnInfoList != null ? columnInfoList.size() : 0;
    }
    
    /**
     * 检查是否包含指定名称的列
     * 
     * @param columnName 列名
     * @return 是否包含该列
     */
    public boolean hasColumn(String columnName) {
        if (columnInfoList == null || columnName == null) {
            return false;
        }
        
        return columnInfoList.stream()
                .anyMatch(column -> columnName.equals(column.getName()));
    }
    
    /**
     * 根据列名获取列信息
     * 
     * @param columnName 列名
     * @return 列信息，如不存在则返回null
     */
    public DatasetColumnInfoDO getColumnByName(String columnName) {
        if (columnInfoList == null || columnName == null) {
            return null;
        }
        
        return columnInfoList.stream()
                .filter(column -> columnName.equals(column.getName()))
                .findFirst()
                .orElse(null);
    }
}
