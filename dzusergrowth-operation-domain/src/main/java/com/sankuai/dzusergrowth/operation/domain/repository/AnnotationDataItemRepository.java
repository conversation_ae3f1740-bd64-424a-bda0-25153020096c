package com.sankuai.dzusergrowth.operation.domain.repository;

import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationDataItemDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.AnnotationDataItemQuery;

import java.util.List;

/**
 * 标注数据条目仓储接口
 */
public interface AnnotationDataItemRepository {
    
    /**
     * 创建新的标注数据条目
     * 
     * @param annotationDataItemDO 标注数据条目信息DO
     */
    void createAnnotationDataItem(AnnotationDataItemDO annotationDataItemDO);
    
    /**
     * 根据ID获取标注数据条目
     * 
     * @param itemId 条目ID
     * @return 标注数据条目信息，如不存在则返回null
     */
    AnnotationDataItemDO getAnnotationDataItemById(Long itemId);
    
    /**
     * 更新标注数据条目
     * 
     * @param annotationDataItemDO 标注数据条目DO
     */
    void updateAnnotationDataItem(AnnotationDataItemDO annotationDataItemDO);
    
    /**
     * 删除标注数据条目
     * 
     * @param itemId 条目ID
     */
    void deleteAnnotationDataItem(Long itemId);
    
    /**
     * 批量查询标注数据条目
     * 
     * @param query 标注数据条目查询条件
     * @return 标注数据条目DO列表
     */
    List<AnnotationDataItemDO> queryAnnotationDataItem(AnnotationDataItemQuery query);
} 