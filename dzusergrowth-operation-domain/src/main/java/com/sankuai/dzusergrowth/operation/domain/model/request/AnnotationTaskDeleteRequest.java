package com.sankuai.dzusergrowth.operation.domain.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标注任务删除请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskDeleteRequest {
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 验证请求参数有效性
     */
    public boolean isValid() {
        return taskId != null && operatorId != null;
    }
} 