package com.sankuai.dzusergrowth.operation.domain.model.request;

import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据集列信息创建请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnCreateRequest {
    
    /**
     * 数据集ID（必填）
     */
    private Long datasetId;
    
    /**
     * 列名（必填）
     */
    private String name;
    
    /**
     * 列展示名（可选）
     */
    private String displayName;
    
    /**
     * 数据类型（必填）
     */
    private DataTypeEnum dataType;
    
    /**
     * 列类型（必填）
     */
    private ColumnTypeEnum columnType;
    
    /**
     * 列配置对象（可选）
     */
    private DatasetColumnConfig columnConfig;
    
    /**
     * 创建人ID（必填）
     */
    private Long creatorId;
    
    /**
     * 验证创建请求参数有效性
     */
    public boolean isValid() {
        // 必填字段验证
        if (datasetId == null) {
            return false;
        }
        
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        if (dataType == null) {
            return false;
        }
        
        if (columnType == null) {
            return false;
        }
        
        if (creatorId == null) {
            return false;
        }

        return true;
    }
    
    /**
     * 验证数据集ID是否有效
     */
    public boolean hasValidDatasetId() {
        return datasetId != null && datasetId > 0;
    }
    
    /**
     * 验证列名格式
     */
    public boolean hasValidName() {
        return name != null && !name.trim().isEmpty() && name.length() <= 100;
    }
    
    /**
     * 验证展示名格式（如果提供）
     */
    public boolean hasValidDisplayName() {
        return displayName == null || (!displayName.trim().isEmpty() && displayName.length() <= 200);
    }
    
    /**
     * 是否提供了展示名
     */
    public boolean hasDisplayName() {
        return displayName != null && !displayName.trim().isEmpty();
    }
    
    /**
     * 获取实际的展示名（如果没有提供展示名，则使用列名）
     */
    public String getActualDisplayName() {
        return hasDisplayName() ? displayName : name;
    }
} 