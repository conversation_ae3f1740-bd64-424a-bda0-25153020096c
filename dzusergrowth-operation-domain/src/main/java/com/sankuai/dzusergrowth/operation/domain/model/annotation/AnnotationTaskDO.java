package com.sankuai.dzusergrowth.operation.domain.model.annotation;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationTaskTypeEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 标注任务领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationTaskDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 标注数据的数据集ID
     */
    private Long annotationDatasetId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 标注类型
     */
    private AnnotationTaskTypeEnum annotationTaskType;
    
    /**
     * 标注配置
     */
    private List<AnnotationConfigDO> annotationConfig;
    
    /**
     * 展示选用的列
     */
    private List<Long> showColumn;
    
    /**
     * 任务标注人员
     */
    private String annotator;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证任务名称不为空
     */
    public boolean isValidTaskName() {
        return taskName != null && !taskName.trim().isEmpty();
    }
    
    /**
     * 验证标注类型是否有效
     */
    public boolean isValidAnnotationType() {
        return annotationTaskType != null;
    }
    
    /**
     * 是否为人工标注
     */
    public boolean isManualAnnotation() {
        return AnnotationTaskTypeEnum.MANUAL.equals(annotationTaskType);
    }
    
    /**
     * 是否为自动标注
     */
    public boolean isAutoAnnotation() {
        return AnnotationTaskTypeEnum.AUTO.equals(annotationTaskType);
    }

    
    /**
     * 获取配置中的所有标注列ID
     */
    public List<Long> getLabelIds() {
        if (annotationConfig == null) {
            return List.of();
        }
        
        return annotationConfig.stream()
                .map(AnnotationConfigDO::getColumnId)
                .filter(java.util.Objects::nonNull)
                .toList();
    }
} 