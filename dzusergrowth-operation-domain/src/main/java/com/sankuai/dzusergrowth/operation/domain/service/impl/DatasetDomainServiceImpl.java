package com.sankuai.dzusergrowth.operation.domain.service.impl;

import com.sankuai.dzusergrowth.common.api.response.PageResult;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetWDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DataItemDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetQuery;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetColumnInfoQuery;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetCreateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetUpdateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnsBatchOperationRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnCreateRequest;
import com.sankuai.dzusergrowth.operation.domain.repository.DatasetRepository;
import com.sankuai.dzusergrowth.operation.domain.repository.DatasetColumnInfoRepository;
import com.sankuai.dzusergrowth.operation.domain.repository.DataItemRepository;
import com.sankuai.dzusergrowth.operation.domain.service.DatasetDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 数据集域服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class DatasetDomainServiceImpl implements DatasetDomainService {

    @Resource
    private DatasetRepository datasetRepository;

    @Resource
    private DatasetColumnInfoRepository datasetColumnInfoRepository;

    @Resource
    private DataItemRepository dataItemRepository;

    @Override
    public DatasetWDO getDatasetAggregateById(Long datasetId) {
        // 参数验证
        if (datasetId == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }

        log.info("开始获取数据集聚合根，数据集ID: {}", datasetId);

        try {
            // 1. 获取数据集基本信息（复用已有方法）
            DatasetDO datasetDO = getDatasetById(datasetId);
            if (datasetDO == null) {
                log.warn("数据集不存在，数据集ID: {}", datasetId);
                return null;
            }

            // 2. 获取数据集的列信息（复用已有方法）
            List<DatasetColumnInfoDO> columnInfoList = getDatasetColumnsByDatasetId(datasetId);

            // 3. 使用静态工厂方法构建聚合根
            DatasetWDO datasetWDO = DatasetWDO.buildFromDatasetDO(datasetDO, columnInfoList);

            log.info("成功获取数据集聚合根，数据集ID: {}，列数量: {}", datasetId, datasetWDO.getColumnCount());
            return datasetWDO;

        } catch (Exception e) {
            log.error("获取数据集聚合根失败，数据集ID: {}", datasetId, e);
            throw new RuntimeException("获取数据集聚合根失败", e);
        }
    }

    @Override
    public DatasetDO getDatasetById(Long datasetId) {
        // 参数验证
        if (datasetId == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }

        log.debug("开始获取数据集基本信息，数据集ID: {}", datasetId);

        try {
            DatasetDO datasetDO = datasetRepository.getDatasetById(datasetId);
            
            if (datasetDO == null) {
                log.warn("数据集不存在，数据集ID: {}", datasetId);
                return null;
            }

            log.debug("成功获取数据集基本信息，数据集ID: {}，名称: {}", datasetId, datasetDO.getName());
            return datasetDO;

        } catch (Exception e) {
            log.error("获取数据集基本信息失败，数据集ID: {}", datasetId, e);
            throw new RuntimeException("获取数据集基本信息失败", e);
        }
    }

    @Override
    public DatasetDO createDataset(DatasetCreateRequest request) {
        // TODO: 实现创建数据集的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }

    @Override
    public DatasetDO updateDataset(DatasetUpdateRequest request) {
        // TODO: 实现更新数据集的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }

    @Override
    public PageResult<DatasetDO> queryDatasetsWithPagination(DatasetQuery query) {
        // TODO: 实现分页查询数据集的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }

    @Override
    public DatasetColumnInfoDO createDatasetColumn(DatasetColumnCreateRequest request) {
        // TODO: 实现创建数据集列的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }

    @Override
    public List<DatasetColumnInfoDO> getDatasetColumnsByDatasetId(Long datasetId) {
        // 参数验证
        if (datasetId == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }

        log.debug("开始获取数据集列信息，数据集ID: {}", datasetId);

        try {
            // 构建查询条件：按创建时间升序排列
            DatasetColumnInfoQuery columnQuery = DatasetColumnInfoQuery.builder()
                    .datasetId(datasetId)
                    .orderBy("add_time")
                    .orderDirection("ASC")
                    .build();

            List<DatasetColumnInfoDO> columnInfoList = datasetColumnInfoRepository.queryDatasetColumnInfo(columnQuery);
            
            if (CollectionUtils.isEmpty(columnInfoList)) {
                log.debug("数据集没有列信息，数据集ID: {}", datasetId);
                return Collections.emptyList();
            }

            log.debug("成功获取数据集列信息，数据集ID: {}，列数量: {}", datasetId, columnInfoList.size());
            return columnInfoList;

        } catch (Exception e) {
            log.error("获取数据集列信息失败，数据集ID: {}", datasetId, e);
            throw new RuntimeException("获取数据集列信息失败", e);
        }
    }

    @Override
    public PageResult<DataItemDO> queryDataItemsByDatasetIdWithPagination(Long datasetId, Integer pageNum, Integer pageSize) {
        // TODO: 实现分页查询数据条目的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }

    @Override
    public void batchOperateDatasetColumns(DatasetColumnsBatchOperationRequest request) {
        // TODO: 实现批量操作数据集列的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }
} 