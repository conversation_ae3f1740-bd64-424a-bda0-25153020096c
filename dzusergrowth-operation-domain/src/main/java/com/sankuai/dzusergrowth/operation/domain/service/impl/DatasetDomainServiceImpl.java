package com.sankuai.dzusergrowth.operation.domain.service.impl;

import com.sankuai.dzusergrowth.common.api.response.PageResult;
import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.TaskStatusEnum;
import com.sankuai.dzusergrowth.operation.api.enums.FixedMetricEnum;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetWDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetColumnInfoDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DataItemDO;
import com.sankuai.dzusergrowth.operation.domain.model.dataset.DatasetGenerateConfig;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetQuery;
import com.sankuai.dzusergrowth.operation.domain.model.query.DatasetColumnInfoQuery;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetCreateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetUpdateRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnsBatchOperationRequest;
import com.sankuai.dzusergrowth.operation.domain.model.request.DatasetColumnCreateRequest;
import com.sankuai.dzusergrowth.operation.domain.repository.DatasetRepository;
import com.sankuai.dzusergrowth.operation.domain.repository.DatasetColumnInfoRepository;
import com.sankuai.dzusergrowth.operation.domain.repository.DataItemRepository;
import com.sankuai.dzusergrowth.operation.domain.service.DatasetDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 数据集域服务实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class DatasetDomainServiceImpl implements DatasetDomainService {

    @Resource
    private DatasetRepository datasetRepository;

    @Resource
    private DatasetColumnInfoRepository datasetColumnInfoRepository;

    @Resource
    private DataItemRepository dataItemRepository;

    @Override
    public DatasetWDO getDatasetAggregateById(Long datasetId) {
        // 参数验证
        if (datasetId == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }

        log.info("开始获取数据集聚合根，数据集ID: {}", datasetId);

        try {
            // 1. 获取数据集基本信息（复用已有方法）
            DatasetDO datasetDO = getDatasetById(datasetId);
            if (datasetDO == null) {
                log.warn("数据集不存在，数据集ID: {}", datasetId);
                return null;
            }

            // 2. 获取数据集的列信息（复用已有方法）
            List<DatasetColumnInfoDO> columnInfoList = getDatasetColumnsByDatasetId(datasetId);

            // 3. 使用静态工厂方法构建聚合根
            DatasetWDO datasetWDO = DatasetWDO.buildFromDatasetDO(datasetDO, columnInfoList);

            log.info("成功获取数据集聚合根，数据集ID: {}，列数量: {}", datasetId, datasetWDO.getColumnCount());
            return datasetWDO;

        } catch (Exception e) {
            log.error("获取数据集聚合根失败，数据集ID: {}", datasetId, e);
            throw new RuntimeException("获取数据集聚合根失败", e);
        }
    }

    @Override
    public DatasetDO getDatasetById(Long datasetId) {
        // 参数验证
        if (datasetId == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }

        log.debug("开始获取数据集基本信息，数据集ID: {}", datasetId);

        try {
            DatasetDO datasetDO = datasetRepository.getDatasetById(datasetId);

            if (datasetDO == null) {
                log.warn("数据集不存在，数据集ID: {}", datasetId);
                return null;
            }

            log.debug("成功获取数据集基本信息，数据集ID: {}，名称: {}", datasetId, datasetDO.getName());
            return datasetDO;

        } catch (Exception e) {
            log.error("获取数据集基本信息失败，数据集ID: {}", datasetId, e);
            throw new RuntimeException("获取数据集基本信息失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DatasetWDO createDataset(DatasetCreateRequest request) {
        // 1. 参数验证
        if (request == null) {
            throw new IllegalArgumentException("创建数据集请求不能为空");
        }

        if (!request.isValid()) {
            throw new IllegalArgumentException("创建数据集请求参数无效");
        }

        log.info("开始创建数据集，名称: {}, 类型: {}, 创建人: {}",
                request.getName(), request.getDatasetType(), request.getCreatorId());

        try {
            // 2. 业务规则验证 - 检查数据集名称是否重复
            validateDatasetNameUnique(request.getName());

            // 3. 创建数据集信息
            DatasetDO datasetDO = createDatasetBasicInfo(request);
            log.info("成功创建数据集基本信息，ID: {}, 名称: {}", datasetDO.getId(), datasetDO.getName());

            // 4. 创建数据集列信息
            List<DatasetColumnInfoDO> columnInfoList = createDefaultDatasetColumns(datasetDO.getId(), request);
            log.info("成功创建数据集列信息，数据集ID: {}, 列数量: {}", datasetDO.getId(), columnInfoList.size());

            // 5. 构建并返回数据集聚合根
            DatasetWDO datasetWDO = DatasetWDO.buildFromDatasetDO(datasetDO, columnInfoList);
            log.info("成功创建数据集聚合根，ID: {}, 名称: {}, 列数量: {}",
                    datasetWDO.getId(), datasetWDO.getName(), datasetWDO.getColumnCount());

            return datasetWDO;

        } catch (IllegalArgumentException | IllegalStateException e) {
            log.error("创建数据集失败，参数或业务规则错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建数据集失败，系统异常，名称: {}", request.getName(), e);
            throw new RuntimeException("创建数据集失败", e);
        }
    }

    @Override
    public DatasetDO updateDataset(DatasetUpdateRequest request) {
        // TODO: 实现更新数据集的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }

    @Override
    public PageResult<DatasetDO> queryDatasetsWithPagination(DatasetQuery query) {
        // TODO: 实现分页查询数据集的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }


    @Override
    public List<DatasetColumnInfoDO> getDatasetColumnsByDatasetId(Long datasetId) {
        // 参数验证
        if (datasetId == null) {
            throw new IllegalArgumentException("数据集ID不能为空");
        }

        log.debug("开始获取数据集列信息，数据集ID: {}", datasetId);

        try {
            // 构建查询条件：按创建时间升序排列
            DatasetColumnInfoQuery columnQuery = DatasetColumnInfoQuery.builder()
                    .datasetId(datasetId)
                    .orderBy("add_time")
                    .orderDirection("ASC")
                    .build();

            List<DatasetColumnInfoDO> columnInfoList = datasetColumnInfoRepository.queryDatasetColumnInfo(columnQuery);

            if (CollectionUtils.isEmpty(columnInfoList)) {
                log.debug("数据集没有列信息，数据集ID: {}", datasetId);
                return Collections.emptyList();
            }

            log.debug("成功获取数据集列信息，数据集ID: {}，列数量: {}", datasetId, columnInfoList.size());
            return columnInfoList;

        } catch (Exception e) {
            log.error("获取数据集列信息失败，数据集ID: {}", datasetId, e);
            throw new RuntimeException("获取数据集列信息失败", e);
        }
    }

    @Override
    public PageResult<DataItemDO> queryDataItemsByDatasetIdWithPagination(Long datasetId, Integer pageNum, Integer pageSize) {
        // TODO: 实现分页查询数据条目的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }

    @Override
    public void batchOperateDatasetColumns(DatasetColumnsBatchOperationRequest request) {
        // TODO: 实现批量操作数据集列的逻辑
        throw new UnsupportedOperationException("方法尚未实现");
    }

    /**
     * 验证数据集名称唯一性
     *
     * @param name 数据集名称
     * @throws IllegalStateException 当数据集名称已存在时抛出
     */
    private void validateDatasetNameUnique(String name) {
        if (!StringUtils.hasText(name)) {
            return;
        }

        // 构建查询条件，查找同名数据集
        DatasetQuery query = DatasetQuery.builder()
                .name(name)
                .pageNum(1)
                .pageSize(1)
                .build();

        try {
            // 统计同名数据集数量
            long count = datasetRepository.countDataset(query);
            if (count > 0) {
                throw new IllegalStateException("数据集名称已存在: " + name);
            }
        } catch (Exception e) {
            if (e instanceof IllegalStateException) {
                throw e;
            }
            log.error("验证数据集名称唯一性失败，名称: {}", name, e);
            throw new RuntimeException("验证数据集名称唯一性失败", e);
        }
    }

    /**
     * 创建数据集基本信息
     *
     * @param request 创建请求
     * @return 创建后的数据集DO
     */
    private DatasetDO createDatasetBasicInfo(DatasetCreateRequest request) {
        Date now = new Date();

        DatasetDO datasetDO = DatasetDO.builder()
                .datasetType(request.getDatasetType())
                .dataGranularity(request.getDataGranularity())
                .name(request.getName())
                .generateType(request.getGenerateType())
                .generateConfig(request.getGenerateConfig())
                .taskStatus(TaskStatusEnum.RUNNING) // 初始状态
                .creatorId(request.getCreatorId())
                .updaterId(request.getCreatorId())
                .description(request.getDescription())
                .addTime(now)
                .updateTime(now)
                .build();

        // 创建数据集，Repository会设置生成的ID到datasetDO中
        datasetRepository.createDataset(datasetDO);
        return datasetDO;
    }

    /**
     * 创建默认的数据集列信息
     *
     * @param datasetId 数据集ID
     * @param request 创建请求
     * @return 创建的列信息列表
     */
    private List<DatasetColumnInfoDO> createDefaultDatasetColumns(Long datasetId, DatasetCreateRequest request) {
        List<DatasetColumnInfoDO> columnInfoList = new ArrayList<>();
        Date now = new Date();
        Long creatorId = request.getCreatorId();

        // 1. 创建基础的会话内容列
        createBasicGeneratedColumns(columnInfoList, datasetId, creatorId, now);

        // 2. 根据生成配置创建固定指标列
        createMetricColumnsFromConfig(columnInfoList, datasetId, request.getGenerateConfig(), creatorId, now);

        // 3. 批量保存列信息
        if (!CollectionUtils.isEmpty(columnInfoList)) {
            for (DatasetColumnInfoDO columnInfo : columnInfoList) {
                datasetColumnInfoRepository.createDatasetColumnInfo(columnInfo);
            }
        }

        return columnInfoList;
    }

    /**
     * 创建基础的会话内容列
     */
    private void createBasicGeneratedColumns(List<DatasetColumnInfoDO> columnInfoList, Long datasetId,
                                           Long creatorId, Date now) {
        // 采集内容列
        columnInfoList.add(DatasetColumnInfoDO.builder()
                .datasetId(datasetId)
                .name("conversationData")
                .displayName("采集会话内容")
                .dataType(DataTypeEnum.STRING)
                .columnType(ColumnTypeEnum.CONVERSATION_DATA)
                .creatorId(creatorId)
                .updaterId(creatorId)
                .addTime(now)
                .updateTime(now)
                .build());

    }

    /**
     * 根据配置创建固定指标列
     */
    private void createMetricColumnsFromConfig(List<DatasetColumnInfoDO> columnInfoList, Long datasetId,
                                             DatasetGenerateConfig generateConfig, Long creatorId, Date now) {
        if (generateConfig == null || CollectionUtils.isEmpty(generateConfig.getCollectIds())) {
            return;
        }

        // 根据指标ID列表创建对应的列
        for (String metricId : generateConfig.getCollectIds()) {
            FixedMetricEnum metric = FixedMetricEnum.fromId(metricId);
            if (metric == null) {
                log.warn("未知的指标ID: {}, 跳过该指标列创建", metricId);
                continue;
            }

            columnInfoList.add(DatasetColumnInfoDO.builder()
                    .datasetId(datasetId)
                    .name(metric.getColumnName())
                    .displayName(metric.getDisplayName())
                    .dataType(metric.getDataType())
                    .columnType(ColumnTypeEnum.GENERATED_DATA)
                    .creatorId(creatorId)
                    .updaterId(creatorId)
                    .addTime(now)
                    .updateTime(now)
                    .build());
        }
    }
} 