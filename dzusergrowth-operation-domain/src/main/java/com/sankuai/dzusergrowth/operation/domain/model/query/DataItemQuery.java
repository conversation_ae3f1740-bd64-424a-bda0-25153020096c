package com.sankuai.dzusergrowth.operation.domain.model.query;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 数据条目查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataItemQuery {
    
    /**
     * 条目ID列表
     */
    private List<Long> itemIds;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据集ID列表
     */
    private List<Long> datasetIds;
    
    /**
     * 数据唯一键
     */
    private String dataUniqueKey;
    
    /**
     * 是否包含已删除数据（默认false，不包含）
     */
    private Boolean includeDeleted;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
} 