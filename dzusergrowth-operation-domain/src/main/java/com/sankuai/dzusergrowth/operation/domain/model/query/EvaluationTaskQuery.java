package com.sankuai.dzusergrowth.operation.domain.model.query;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 评测任务查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationTaskQuery {
    
    /**
     * 任务ID列表
     */
    private List<Long> taskIds;
    
    /**
     * 资源测的数据集ID
     */
    private Long sourceDatasetId;
    
    /**
     * 任务名称（模糊查询）
     */
    private String taskName;
    
    /**
     * 评测类型: 1-手动评测; 2-自动检验
     */
    private Integer evaluationType;
    
    /**
     * 产出数据集ID
     */
    private Long evaluationDatasetId;
    
    /**
     * 任务执行状态
     */
    private Integer status;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
} 