package com.sankuai.dzusergrowth.operation.domain.model.annotation;

import com.sankuai.dzusergrowth.operation.api.enums.AnnotationLabelTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标注配置领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationConfigDO {
    
    /**
     * 标注列Id (从数据集列信息表中映射得到)
     */
    private Long labelId;
    
    /**
     * 标签类型
     */
    private AnnotationLabelTypeEnum labelType;
    
    /**
     * 标签选项（仅枚举类型有值）
     */
    private List<String> labelOption;
    
    /**
     * 是否多选
     */
    private Boolean isMultiSelected;
    
    /**
     * 是否必选
     */
    private Boolean isRequired;
    
    /**
     * 列展示名
     */
    private String displayName;
}