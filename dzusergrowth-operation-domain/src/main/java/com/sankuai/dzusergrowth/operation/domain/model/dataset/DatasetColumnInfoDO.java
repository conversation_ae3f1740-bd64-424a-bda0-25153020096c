package com.sankuai.dzusergrowth.operation.domain.model.dataset;

import com.sankuai.dzusergrowth.operation.api.enums.DataTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.ColumnTypeEnum;
import lombok.*;

import java.util.Date;

/**
 * 数据集列信息领域对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetColumnInfoDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 列名
     */
    private String name;
    
    /**
     * 列展示名
     */
    private String displayName;
    
    /**
     * 数据类型
     */
    private DataTypeEnum dataType;
    
    /**
     * 列类型
     */
    private ColumnTypeEnum columnType;
    
    /**
     * 列配置对象
     * 在存储时会转换为JSON字符串
     */
    private DatasetColumnConfig columnConfig;

    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 更新人ID
     */
    private Long updaterId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 验证列名不为空
     */
    public boolean isValidName() {
        return name != null && !name.trim().isEmpty();
    }
    
    /**
     * 验证数据类型是否有效
     */
    public boolean isValidDataType() {
        return dataType != null;
    }
    
    /**
     * 验证列类型是否有效
     */
    public boolean isValidColumnType() {
        return columnType != null;
    }

} 