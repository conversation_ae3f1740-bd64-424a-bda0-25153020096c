package com.sankuai.dzusergrowth.operation.domain.model.query;

import com.sankuai.dzusergrowth.operation.api.enums.DatasetTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.DataGranularityEnum;
import com.sankuai.dzusergrowth.operation.api.enums.GenerateTypeEnum;
import com.sankuai.dzusergrowth.operation.api.enums.AssetTypeEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 数据集查询条件
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetQuery {
    
    /**
     * 数据集ID列表
     */
    private List<Long> datasetIds;
    
    /**
     * 数据集类型: 1-原始数据集; 2-评测结果集
     */
    private Integer datasetType;
    
    /**
     * 数据粒度: 1-活动; 2-任务; 3-query; 4-log
     */
    private Integer dataGranularity;

    /**
     * 活动类型: 1-活动投放; 2-内容营销
     * 需要从generateConfig的JSON中解析activity_type字段
     */
    private Integer activityType;

    /**
     * 资产类型: 1-笔记; 2-图片; 3-会场
     * 需要从generateConfig的JSON中解析asset_type字段
     */
    private Integer assetType;
    
    /**
     * 数据集名称（模糊查询）
     */
    private String name;
    
    /**
     * 生成方式: 1-条件来源; 2-手动上传; 3-明确来源
     */
    private Integer generateType;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间开始
     */
    private Date addTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date addTimeEnd;
    
    /**
     * 分页页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC/DESC
     */
    private String orderDirection;
    
    /**
     * 通过数据集类型枚举设置查询条件
     *
     * @param datasetTypeEnum 数据集类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQuery setDatasetType(DatasetTypeEnum datasetTypeEnum) {
        this.datasetType = datasetTypeEnum != null ? datasetTypeEnum.getCode() : null;
        return this;
    }
    
    /**
     * 通过数据粒度枚举设置查询条件
     *
     * @param dataGranularityEnum 数据粒度枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQuery setDataGranularity(DataGranularityEnum dataGranularityEnum) {
        this.dataGranularity = dataGranularityEnum != null ? dataGranularityEnum.getCode() : null;
        return this;
    }
    
    /**
     * 通过生成方式枚举设置查询条件
     *
     * @param generateTypeEnum 生成方式枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQuery setGenerateType(GenerateTypeEnum generateTypeEnum) {
        this.generateType = generateTypeEnum != null ? generateTypeEnum.getCode() : null;
        return this;
    }
    
    /**
     * 通过资产类型枚举设置查询条件
     *
     * @param assetTypeEnum 资产类型枚举
     * @return 当前查询对象，支持链式调用
     */
    public DatasetQuery setAssetType(AssetTypeEnum assetTypeEnum) {
        this.assetType = assetTypeEnum != null ? assetTypeEnum.getCode() : null;
        return this;
    }
} 