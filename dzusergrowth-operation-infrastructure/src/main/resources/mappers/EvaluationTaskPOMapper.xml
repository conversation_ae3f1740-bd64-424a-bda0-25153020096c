<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.EvaluationTaskPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_dataset_id" jdbcType="BIGINT" property="sourceDatasetId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="evaluation_type" jdbcType="INTEGER" property="evaluationType" />
    <result column="evaluation_config" jdbcType="CHAR" property="evaluationConfig" />
    <result column="evaluation_dataset_id" jdbcType="BIGINT" property="evaluationDatasetId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="evaluation_data" jdbcType="CHAR" property="evaluationData" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source_dataset_id, task_name, evaluation_type, evaluation_config, evaluation_dataset_id, 
    status, evaluation_data, add_time, update_time, creator_id, updater_id
  </sql>
  <sql id="Blob_Column_List">
    description
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.EvaluationTaskPOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_evaluation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.EvaluationTaskPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_evaluation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_evaluation_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ai_admin_evaluation_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.EvaluationTaskPOExample">
    delete from ai_admin_evaluation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO">
    insert into ai_admin_evaluation_task (id, source_dataset_id, task_name, 
      evaluation_type, evaluation_config, evaluation_dataset_id, 
      status, evaluation_data, add_time, 
      update_time, creator_id, updater_id, 
      description)
    values (#{id,jdbcType=BIGINT}, #{sourceDatasetId,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, 
      #{evaluationType,jdbcType=INTEGER}, #{evaluationConfig,jdbcType=CHAR}, #{evaluationDatasetId,jdbcType=BIGINT}, 
      #{status,jdbcType=INTEGER}, #{evaluationData,jdbcType=CHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{creatorId,jdbcType=BIGINT}, #{updaterId,jdbcType=BIGINT}, 
      #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO">
    insert into ai_admin_evaluation_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceDatasetId != null">
        source_dataset_id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="evaluationType != null">
        evaluation_type,
      </if>
      <if test="evaluationConfig != null">
        evaluation_config,
      </if>
      <if test="evaluationDatasetId != null">
        evaluation_dataset_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="evaluationData != null">
        evaluation_data,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sourceDatasetId != null">
        #{sourceDatasetId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="evaluationType != null">
        #{evaluationType,jdbcType=INTEGER},
      </if>
      <if test="evaluationConfig != null">
        #{evaluationConfig,jdbcType=CHAR},
      </if>
      <if test="evaluationDatasetId != null">
        #{evaluationDatasetId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="evaluationData != null">
        #{evaluationData,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.EvaluationTaskPOExample" resultType="java.lang.Long">
    select count(*) from ai_admin_evaluation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ai_admin_evaluation_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sourceDatasetId != null">
        source_dataset_id = #{record.sourceDatasetId,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluationType != null">
        evaluation_type = #{record.evaluationType,jdbcType=INTEGER},
      </if>
      <if test="record.evaluationConfig != null">
        evaluation_config = #{record.evaluationConfig,jdbcType=CHAR},
      </if>
      <if test="record.evaluationDatasetId != null">
        evaluation_dataset_id = #{record.evaluationDatasetId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.evaluationData != null">
        evaluation_data = #{record.evaluationData,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.updaterId != null">
        updater_id = #{record.updaterId,jdbcType=BIGINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update ai_admin_evaluation_task
    set id = #{record.id,jdbcType=BIGINT},
      source_dataset_id = #{record.sourceDatasetId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      evaluation_type = #{record.evaluationType,jdbcType=INTEGER},
      evaluation_config = #{record.evaluationConfig,jdbcType=CHAR},
      evaluation_dataset_id = #{record.evaluationDatasetId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      evaluation_data = #{record.evaluationData,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      updater_id = #{record.updaterId,jdbcType=BIGINT},
      description = #{record.description,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ai_admin_evaluation_task
    set id = #{record.id,jdbcType=BIGINT},
      source_dataset_id = #{record.sourceDatasetId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      evaluation_type = #{record.evaluationType,jdbcType=INTEGER},
      evaluation_config = #{record.evaluationConfig,jdbcType=CHAR},
      evaluation_dataset_id = #{record.evaluationDatasetId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      evaluation_data = #{record.evaluationData,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      updater_id = #{record.updaterId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO">
    update ai_admin_evaluation_task
    <set>
      <if test="sourceDatasetId != null">
        source_dataset_id = #{sourceDatasetId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="evaluationType != null">
        evaluation_type = #{evaluationType,jdbcType=INTEGER},
      </if>
      <if test="evaluationConfig != null">
        evaluation_config = #{evaluationConfig,jdbcType=CHAR},
      </if>
      <if test="evaluationDatasetId != null">
        evaluation_dataset_id = #{evaluationDatasetId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="evaluationData != null">
        evaluation_data = #{evaluationData,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO">
    update ai_admin_evaluation_task
    set source_dataset_id = #{sourceDatasetId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      evaluation_type = #{evaluationType,jdbcType=INTEGER},
      evaluation_config = #{evaluationConfig,jdbcType=CHAR},
      evaluation_dataset_id = #{evaluationDatasetId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      evaluation_data = #{evaluationData,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator_id = #{creatorId,jdbcType=BIGINT},
      updater_id = #{updaterId,jdbcType=BIGINT},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.EvaluationTaskPO">
    update ai_admin_evaluation_task
    set source_dataset_id = #{sourceDatasetId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      evaluation_type = #{evaluationType,jdbcType=INTEGER},
      evaluation_config = #{evaluationConfig,jdbcType=CHAR},
      evaluation_dataset_id = #{evaluationDatasetId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      evaluation_data = #{evaluationData,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator_id = #{creatorId,jdbcType=BIGINT},
      updater_id = #{updaterId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.EvaluationTaskPOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_evaluation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.EvaluationTaskPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_evaluation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
</mapper>