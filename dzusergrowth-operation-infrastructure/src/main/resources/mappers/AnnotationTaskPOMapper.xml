<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.AnnotationTaskPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="annotation_dataset_id" jdbcType="BIGINT" property="annotationDatasetId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="annotation_type" jdbcType="INTEGER" property="annotationType" />
    <result column="annotation_config" jdbcType="CHAR" property="annotationConfig" />
    <result column="show_column" jdbcType="CHAR" property="showColumn" />
    <result column="annotator" jdbcType="VARCHAR" property="annotator" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, annotation_dataset_id, task_name, annotation_type, annotation_config, show_column, 
    annotator, add_time, update_time, creator_id, updater_id
  </sql>
  <sql id="Blob_Column_List">
    description
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationTaskPOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_annotation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationTaskPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_annotation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_annotation_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ai_admin_annotation_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationTaskPOExample">
    delete from ai_admin_annotation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO">
    insert into ai_admin_annotation_task (id, annotation_dataset_id, task_name, 
      annotation_type, annotation_config, show_column, 
      annotator, add_time, update_time, 
      creator_id, updater_id, description
      )
    values (#{id,jdbcType=BIGINT}, #{annotationDatasetId,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, 
      #{annotationType,jdbcType=INTEGER}, #{annotationConfig,jdbcType=CHAR}, #{showColumn,jdbcType=CHAR}, 
      #{annotator,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creatorId,jdbcType=BIGINT}, #{updaterId,jdbcType=BIGINT}, #{description,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO">
    insert into ai_admin_annotation_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="annotationDatasetId != null">
        annotation_dataset_id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="annotationType != null">
        annotation_type,
      </if>
      <if test="annotationConfig != null">
        annotation_config,
      </if>
      <if test="showColumn != null">
        show_column,
      </if>
      <if test="annotator != null">
        annotator,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="annotationDatasetId != null">
        #{annotationDatasetId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="annotationType != null">
        #{annotationType,jdbcType=INTEGER},
      </if>
      <if test="annotationConfig != null">
        #{annotationConfig,jdbcType=CHAR},
      </if>
      <if test="showColumn != null">
        #{showColumn,jdbcType=CHAR},
      </if>
      <if test="annotator != null">
        #{annotator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationTaskPOExample" resultType="java.lang.Long">
    select count(*) from ai_admin_annotation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ai_admin_annotation_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.annotationDatasetId != null">
        annotation_dataset_id = #{record.annotationDatasetId,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.annotationType != null">
        annotation_type = #{record.annotationType,jdbcType=INTEGER},
      </if>
      <if test="record.annotationConfig != null">
        annotation_config = #{record.annotationConfig,jdbcType=CHAR},
      </if>
      <if test="record.showColumn != null">
        show_column = #{record.showColumn,jdbcType=CHAR},
      </if>
      <if test="record.annotator != null">
        annotator = #{record.annotator,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.updaterId != null">
        updater_id = #{record.updaterId,jdbcType=BIGINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update ai_admin_annotation_task
    set id = #{record.id,jdbcType=BIGINT},
      annotation_dataset_id = #{record.annotationDatasetId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      annotation_type = #{record.annotationType,jdbcType=INTEGER},
      annotation_config = #{record.annotationConfig,jdbcType=CHAR},
      show_column = #{record.showColumn,jdbcType=CHAR},
      annotator = #{record.annotator,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      updater_id = #{record.updaterId,jdbcType=BIGINT},
      description = #{record.description,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ai_admin_annotation_task
    set id = #{record.id,jdbcType=BIGINT},
      annotation_dataset_id = #{record.annotationDatasetId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      annotation_type = #{record.annotationType,jdbcType=INTEGER},
      annotation_config = #{record.annotationConfig,jdbcType=CHAR},
      show_column = #{record.showColumn,jdbcType=CHAR},
      annotator = #{record.annotator,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      updater_id = #{record.updaterId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO">
    update ai_admin_annotation_task
    <set>
      <if test="annotationDatasetId != null">
        annotation_dataset_id = #{annotationDatasetId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="annotationType != null">
        annotation_type = #{annotationType,jdbcType=INTEGER},
      </if>
      <if test="annotationConfig != null">
        annotation_config = #{annotationConfig,jdbcType=CHAR},
      </if>
      <if test="showColumn != null">
        show_column = #{showColumn,jdbcType=CHAR},
      </if>
      <if test="annotator != null">
        annotator = #{annotator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO">
    update ai_admin_annotation_task
    set annotation_dataset_id = #{annotationDatasetId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      annotation_type = #{annotationType,jdbcType=INTEGER},
      annotation_config = #{annotationConfig,jdbcType=CHAR},
      show_column = #{showColumn,jdbcType=CHAR},
      annotator = #{annotator,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator_id = #{creatorId,jdbcType=BIGINT},
      updater_id = #{updaterId,jdbcType=BIGINT},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO">
    update ai_admin_annotation_task
    set annotation_dataset_id = #{annotationDatasetId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      annotation_type = #{annotationType,jdbcType=INTEGER},
      annotation_config = #{annotationConfig,jdbcType=CHAR},
      show_column = #{showColumn,jdbcType=CHAR},
      annotator = #{annotator,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator_id = #{creatorId,jdbcType=BIGINT},
      updater_id = #{updaterId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationTaskPOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_admin_annotation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationTaskPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_annotation_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
</mapper>