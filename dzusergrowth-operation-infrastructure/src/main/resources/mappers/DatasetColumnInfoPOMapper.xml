<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.DatasetColumnInfoPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetColumnInfoPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dataset_id" jdbcType="BIGINT" property="datasetId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="display_name" jdbcType="VARCHAR" property="displayName" />
    <result column="data_type" jdbcType="INTEGER" property="dataType" />
    <result column="column_type" jdbcType="INTEGER" property="columnType" />
    <result column="column_config" jdbcType="CHAR" property="columnConfig" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dataset_id, name, display_name, data_type, column_type, column_config, add_time, 
    update_time, creator_id, updater_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetColumnInfoPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_dataset_column_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ai_admin_dataset_column_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ai_admin_dataset_column_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetColumnInfoPOExample">
    delete from ai_admin_dataset_column_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetColumnInfoPO">
    insert into ai_admin_dataset_column_info (id, dataset_id, name, 
      display_name, data_type, column_type, 
      column_config, add_time, update_time, 
      creator_id, updater_id)
    values (#{id,jdbcType=BIGINT}, #{datasetId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{displayName,jdbcType=VARCHAR}, #{dataType,jdbcType=INTEGER}, #{columnType,jdbcType=INTEGER}, 
      #{columnConfig,jdbcType=CHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creatorId,jdbcType=BIGINT}, #{updaterId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetColumnInfoPO">
    insert into ai_admin_dataset_column_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="datasetId != null">
        dataset_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="displayName != null">
        display_name,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="columnType != null">
        column_type,
      </if>
      <if test="columnConfig != null">
        column_config,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="datasetId != null">
        #{datasetId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null">
        #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=INTEGER},
      </if>
      <if test="columnType != null">
        #{columnType,jdbcType=INTEGER},
      </if>
      <if test="columnConfig != null">
        #{columnConfig,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetColumnInfoPOExample" resultType="java.lang.Long">
    select count(*) from ai_admin_dataset_column_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ai_admin_dataset_column_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.datasetId != null">
        dataset_id = #{record.datasetId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.displayName != null">
        display_name = #{record.displayName,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=INTEGER},
      </if>
      <if test="record.columnType != null">
        column_type = #{record.columnType,jdbcType=INTEGER},
      </if>
      <if test="record.columnConfig != null">
        column_config = #{record.columnConfig,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.updaterId != null">
        updater_id = #{record.updaterId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ai_admin_dataset_column_info
    set id = #{record.id,jdbcType=BIGINT},
      dataset_id = #{record.datasetId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      display_name = #{record.displayName,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=INTEGER},
      column_type = #{record.columnType,jdbcType=INTEGER},
      column_config = #{record.columnConfig,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      updater_id = #{record.updaterId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetColumnInfoPO">
    update ai_admin_dataset_column_info
    <set>
      <if test="datasetId != null">
        dataset_id = #{datasetId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null">
        display_name = #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=INTEGER},
      </if>
      <if test="columnType != null">
        column_type = #{columnType,jdbcType=INTEGER},
      </if>
      <if test="columnConfig != null">
        column_config = #{columnConfig,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.po.DatasetColumnInfoPO">
    update ai_admin_dataset_column_info
    set dataset_id = #{datasetId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      display_name = #{displayName,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=INTEGER},
      column_type = #{columnType,jdbcType=INTEGER},
      column_config = #{columnConfig,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator_id = #{creatorId,jdbcType=BIGINT},
      updater_id = #{updaterId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.sankuai.dzusergrowth.operation.infrastructure.dal.example.DatasetColumnInfoPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_admin_dataset_column_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
</mapper>