package com.sankuai.dzusergrowth.operation.infrastructure.repository.impl;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationDataItemDO;
import com.sankuai.dzusergrowth.operation.domain.model.query.AnnotationDataItemQuery;
import com.sankuai.dzusergrowth.operation.domain.repository.AnnotationDataItemRepository;
import com.sankuai.dzusergrowth.operation.infrastructure.converter.AnnotationDataItemConverter;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.example.AnnotationDataItemPOExample;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.mapper.AnnotationDataItemPOMapper;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationDataItemPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标注数据条目仓储实现类
 */
@Repository
public class AnnotationDataItemRepositoryImpl implements AnnotationDataItemRepository {

    @Resource
    private AnnotationDataItemPOMapper annotationDataItemMapper;

    /**
     * 创建新的标注数据条目
     *
     * @param annotationDataItemDO 标注数据条目信息DO
     */
    @Override
    public void createAnnotationDataItem(AnnotationDataItemDO annotationDataItemDO) {
        if (annotationDataItemDO == null) {
            throw new IllegalArgumentException("标注数据条目对象不能为空");
        }
        
        // 根据AnnotationDataItemDO构建AnnotationDataItem PO对象
        AnnotationDataItemPO annotationDataItemPO = AnnotationDataItemConverter.convertDOToPO(annotationDataItemDO);
        // 保存标注数据条目到数据库
        annotationDataItemMapper.insertSelective(annotationDataItemPO);
        
        // 设置生成的ID
        annotationDataItemDO.setId(annotationDataItemPO.getId());
    }

    /**
     * 根据ID获取标注数据条目
     *
     * @param itemId 条目ID
     * @return 标注数据条目信息，如不存在则返回null
     */
    @Override
    public AnnotationDataItemDO getAnnotationDataItemById(Long itemId) {
        if (itemId == null) {
            return null;
        }
        
        AnnotationDataItemPOExample example = new AnnotationDataItemPOExample();
        example.createCriteria().andIdEqualTo(itemId);

        // 使用ZebraForceMasterHelper强制查询主库
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            List<AnnotationDataItemPO> annotationDataItemPOS = annotationDataItemMapper.selectByExample(example);
            if (annotationDataItemPOS == null || annotationDataItemPOS.isEmpty()) {
                return null;
            }

            return AnnotationDataItemConverter.convertPOToDO(annotationDataItemPOS.get(0));
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }
    
    /**
     * 更新标注数据条目
     *
     * @param annotationDataItemDO 标注数据条目DO
     */
    @Override
    public void updateAnnotationDataItem(AnnotationDataItemDO annotationDataItemDO) {
        if (annotationDataItemDO == null) {
            throw new IllegalArgumentException("标注数据条目对象不能为空");
        }
        
        if (annotationDataItemDO.getId() == null) {
            throw new IllegalArgumentException("标注数据条目ID不能为空");
        }
        
        // 构建更新对象
        AnnotationDataItemPO annotationDataItemPO = AnnotationDataItemConverter.convertDOToPO(annotationDataItemDO);
        
        // 构建条件
        AnnotationDataItemPOExample example = new AnnotationDataItemPOExample();
        example.createCriteria().andIdEqualTo(annotationDataItemDO.getId());
        
        // 执行更新
        int result = annotationDataItemMapper.updateByExampleSelective(annotationDataItemPO, example);
        
        if (result != 1) {
            throw new IllegalStateException("更新标注数据条目失败，条目ID: " + annotationDataItemDO.getId() + "，可能条目不存在");
        }
    }
    
    /**
     * 删除标注数据条目
     *
     * @param itemId 条目ID
     */
    @Override
    public void deleteAnnotationDataItem(Long itemId) {
        if (itemId == null) {
            throw new IllegalArgumentException("标注数据条目ID不能为空");
        }
        
        AnnotationDataItemPOExample example = new AnnotationDataItemPOExample();
        example.createCriteria().andIdEqualTo(itemId);
        
        int result = annotationDataItemMapper.deleteByExample(example);
        
        if (result != 1) {
            throw new IllegalStateException("删除标注数据条目失败，条目ID: " + itemId + "，可能条目不存在");
        }
    }
    
    /**
     * 批量查询标注数据条目
     *
     * @param query 标注数据条目查询条件
     * @return 标注数据条目DO列表
     */
    @Override
    public List<AnnotationDataItemDO> queryAnnotationDataItem(AnnotationDataItemQuery query) {
        if (query == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        
        AnnotationDataItemPOExample example = new AnnotationDataItemPOExample();
        AnnotationDataItemPOExample.Criteria criteria = example.createCriteria();
        
        // 构建查询条件
        if (!CollectionUtils.isEmpty(query.getItemIds())) {
            criteria.andIdIn(query.getItemIds());
        }
        
        if (query.getDatasetId() != null) {
            criteria.andDatasetIdEqualTo(query.getDatasetId());
        }
        
        if (!CollectionUtils.isEmpty(query.getDatasetIds())) {
            criteria.andDatasetIdIn(query.getDatasetIds());
        }
        
        if (StringUtils.hasText(query.getDataUniqueKey())) {
            criteria.andDataUniqueKeyEqualTo(query.getDataUniqueKey());
        }
        
        if (query.getTaskId() != null) {
            criteria.andTaskIdEqualTo(query.getTaskId());
        }
        
        if (!CollectionUtils.isEmpty(query.getTaskIds())) {
            criteria.andTaskIdIn(query.getTaskIds());
        }
        
        if (query.getEffectiveStatus() != null) {
            criteria.andStatusEqualTo(query.getEffectiveStatus());
        }
        
        if (query.getCreatorId() != null) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }
        
        if (query.getAddTimeStart() != null) {
            criteria.andAddTimeGreaterThanOrEqualTo(query.getAddTimeStart());
        }
        
        if (query.getAddTimeEnd() != null) {
            criteria.andAddTimeLessThanOrEqualTo(query.getAddTimeEnd());
        }
        
        // 设置排序
        if (StringUtils.hasText(query.getOrderBy())) {
            String orderClause = query.getOrderBy();
            if (StringUtils.hasText(query.getOrderDirection())) {
                orderClause += " " + query.getOrderDirection();
            }
            example.setOrderByClause(orderClause);
        } else {
            example.setOrderByClause("add_time DESC");
        }
        
        // 处理分页
        if (query.getPageSize() != null && query.getPageSize() > 0) {
            int pageNum = query.getPageNum() != null && query.getPageNum() > 0 ? query.getPageNum() : 1;
            int offset = (pageNum - 1) * query.getPageSize();
            example.setOffset(offset);
            example.setRows(query.getPageSize());
        }
        
        List<AnnotationDataItemPO> annotationDataItemPOS = annotationDataItemMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(annotationDataItemPOS)) {
            return Collections.emptyList();
        }
        
        return annotationDataItemPOS.stream()
                .map(AnnotationDataItemConverter::convertPOToDO)
                .collect(Collectors.toList());
    }
} 