package com.sankuai.dzusergrowth.operation.infrastructure.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzusergrowth.operation.api.enums.AnnotationTaskTypeEnum;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationConfigDO;
import com.sankuai.dzusergrowth.operation.domain.model.annotation.AnnotationTaskDO;
import com.sankuai.dzusergrowth.operation.infrastructure.dal.po.AnnotationTaskPO;
import com.sankuai.dzusergrowth.operation.infrastructure.utils.JsonUtils;

import java.util.Collections;
import java.util.List;

/**
 * 标注任务转换器
 */
public class AnnotationTaskConverter {

    /**
     * DO转PO
     */
    public static AnnotationTaskPO convertDOToPO(AnnotationTaskDO annotationTaskDO) {
        if (annotationTaskDO == null) {
            return null;
        }
        
        AnnotationTaskPO annotationTaskPO = new AnnotationTaskPO();
        annotationTaskPO.setId(annotationTaskDO.getId());
        annotationTaskPO.setAnnotationDatasetId(annotationTaskDO.getAnnotationDatasetId());
        annotationTaskPO.setTaskName(annotationTaskDO.getTaskName());
        
        // 枚举转换为Integer
        annotationTaskPO.setAnnotationType(
                annotationTaskDO.getAnnotationTaskType() != null ? 
                annotationTaskDO.getAnnotationTaskType().getCode() : null);
        
        // List<AnnotationConfigDO>转换为JSON字符串
        annotationTaskPO.setAnnotationConfig(
                convertAnnotationConfigToJson(annotationTaskDO.getAnnotationConfig()));
        
        // List<Long>转换为JSON字符串
        annotationTaskPO.setShowColumn(convertShowColumnToJson(annotationTaskDO.getShowColumn()));
        annotationTaskPO.setAnnotator(annotationTaskDO.getAnnotator());
        annotationTaskPO.setCreatorId(annotationTaskDO.getCreatorId());
        annotationTaskPO.setUpdaterId(annotationTaskDO.getUpdaterId());
        annotationTaskPO.setDescription(annotationTaskDO.getDescription());
        annotationTaskPO.setAddTime(annotationTaskDO.getAddTime());
        annotationTaskPO.setUpdateTime(annotationTaskDO.getUpdateTime());
        
        return annotationTaskPO;
    }

    /**
     * PO转DO
     */
    public static AnnotationTaskDO convertPOToDO(AnnotationTaskPO annotationTaskPO) {
        if (annotationTaskPO == null) {
            return null;
        }
        
        return AnnotationTaskDO.builder()
                .id(annotationTaskPO.getId())
                .annotationDatasetId(annotationTaskPO.getAnnotationDatasetId())
                .taskName(annotationTaskPO.getTaskName())
                
                // Integer转换为枚举
                .annotationTaskType(AnnotationTaskTypeEnum.fromCode(annotationTaskPO.getAnnotationType()))
                
                // JSON字符串转换为List<AnnotationConfigDO>
                .annotationConfig(convertJsonToAnnotationConfig(annotationTaskPO.getAnnotationConfig()))
                
                // JSON字符串转换为List<Long>
                .showColumn(convertJsonToShowColumn(annotationTaskPO.getShowColumn()))
                .annotator(annotationTaskPO.getAnnotator())
                .creatorId(annotationTaskPO.getCreatorId())
                .updaterId(annotationTaskPO.getUpdaterId())
                .description(annotationTaskPO.getDescription())
                .addTime(annotationTaskPO.getAddTime())
                .updateTime(annotationTaskPO.getUpdateTime())
                .build();
    }
    
    /**
     * 标注配置列表转换为JSON字符串
     */
    private static String convertAnnotationConfigToJson(List<AnnotationConfigDO> annotationConfigList) {
        if (annotationConfigList == null || annotationConfigList.isEmpty()) {
            return null;
        }
        
        // 先转换为DTO列表，再序列化为JSON
        List<AnnotationConfigDO> dtoList = annotationConfigList.stream()
                .map(AnnotationTaskConverter::convertDOToDTO)
                .toList();
        return JsonUtils.toJsonString(dtoList);
    }
    
    /**
     * JSON字符串转换为标注配置列表
     */
    private static List<AnnotationConfigDO> convertJsonToAnnotationConfig(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        TypeReference<List<AnnotationConfigDO>> typeRef = new TypeReference<List<AnnotationConfigDO>>() {};
        List<AnnotationConfigDO> dtoList = JsonUtils.fromJsonString(jsonStr, typeRef);
        
        if (dtoList == null) {
            return Collections.emptyList();
        }
        
        // 将DTO列表转换为DO列表
        return dtoList.stream()
                .map(AnnotationTaskConverter::convertDTOToDO)
                .toList();
    }
    
    /**
     * AnnotationConfigDO转换为AnnotationConfigDO
     */
    private static AnnotationConfigDO convertDOToDTO(AnnotationConfigDO configDO) {
        if (configDO == null) {
            return null;
        }
        
        return AnnotationConfigDO.builder()
                .labelId(configDO.getLabelId())
                .labelType(configDO.getLabelType())
                .labelOption(configDO.getLabelOption())
                .isMultiSelected(configDO.getIsMultiSelected())
                .isRequired(configDO.getIsRequired())
                .displayName(configDO.getDisplayName())
                .build();
    }
    
    /**
     * AnnotationConfigDO转换为AnnotationConfigDO
     */
    private static AnnotationConfigDO convertDTOToDO(AnnotationConfigDO configDTO) {
        if (configDTO == null) {
            return null;
        }
        
        return AnnotationConfigDO.builder()
                .labelId(configDTO.getLabelId())
                .labelType(configDTO.getLabelType())
                .labelOption(configDTO.getLabelOption())
                .isMultiSelected(configDTO.getIsMultiSelected())
                .isRequired(configDTO.getIsRequired())
                .displayName(configDTO.getDisplayName())
                .build();
    }
    
    /**
     * 将展示列列表转换为JSON字符串
     */
    private static String convertShowColumnToJson(List<Long> showColumnList) {
        if (showColumnList == null || showColumnList.isEmpty()) {
            return null;
        }
        return JsonUtils.toJsonString(showColumnList);
    }
    
    /**
     * 将JSON字符串转换为展示列列表
     */
    private static List<Long> convertJsonToShowColumn(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        TypeReference<List<Long>> typeRef = new TypeReference<List<Long>>() {};
        List<Long> columnList = JsonUtils.fromJsonString(jsonStr, typeRef);
        
        return columnList != null ? columnList : Collections.emptyList();
    }
} 